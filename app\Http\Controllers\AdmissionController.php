<?php

namespace App\Http\Controllers;

use App\Models\Admission;
use Illuminate\Http\Request;
use App\Trait\SaveFile;
use App\Mail\AdmissionMail;
use App\Mail\ConfirmationMail;
use Mail;

class AdmissionController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $admissions=Admission::select('id','status','class_program','name','father_name','mother_name','dob','gender','address','mobile1');
        if(!empty($request['name'])){
            $name=$request['name'];
            $admissions=$admissions->where('name','LIKE','%'.$name.'%');
        }else{
            $name='';
        }
        if(!empty($request['mobile1'])){
            $mobile1=$request['mobile1'];
            $admissions=$admissions->where('mobile1',$mobile1);
        }else{
            $mobile1='';
        }
        $admissions=$admissions->orderBy('id','desc')->paginate(10);
        return view('app.admission.show',compact('admissions','name','mobile1'));
    }

    public function approved(Request $request){
        $approved_admissions=Admission::select('id','status','class_program','name','father_name','mother_name','dob','gender','address','mobile1');
        if(!empty($request['name'])){
            $name=$request['name'];
            $approved_admissions=$approved_admissions->where('name','LIKE','%'.$name.'%');
        }else{
            $name='';
        }
        if(!empty($request['mobile1'])){
            $mobile1=$request['mobile1'];
            $approved_admissions=$approved_admissions->where('mobile1',$mobile1);
        }else{
            $mobile1='';
        }
        $approved_admissions=$approved_admissions->orderBy('id','desc')->where('status',1)->paginate(10);
        return view('app.admission.approved_show',compact('approved_admissions','name','mobile1'));
    }

    public function declined(Request $request){
        $declined_admissions=Admission::select('id','status','class_program','name','father_name','mother_name','dob','gender','address','mobile1');
        if(!empty($request['name'])){
            $name=$request['name'];
            $declined_admissions=$declined_admissions->where('name','LIKE','%'.$name.'%');
        }else{
            $name='';
        }
        if(!empty($request['mobile1'])){
            $mobile1=$request['mobile1'];
            $declined_admissions=$declined_admissions->where('mobile1',$mobile1);
        }else{
            $mobile1='';
        }
        $declined_admissions=$declined_admissions->orderBy('id','desc')->where('status',2)->paginate(10);
        return view('app.admission.declined_show',compact('declined_admissions','name','mobile1'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'class_program'=>'required|max:225|string',
            'name'=>'required|max:70|min:3|string',
            'father_name'=>'required|max:70|min:3|string',
            'mother_name'=>'required|max:70|min:3|string',
            'dob'=>'required|date',
            'gender'=>'required',
            'blood_group'=>'nullable|string|max:2|min:2',
            'parent_occupation'=>'required|string|max:70',
            'address'=>'required|string|max:70',
            'caste'=>'required|string|max:70',
            'mobile1'=>'required|string|max:10|min:10',
            'mobile2'=>'nullable|string|max:10|min:10',
            'email'=>'nullable|max:100|email',
            'birth_certificate'=>'required|string|max:15',
            'aadhar_card_candidate'=>'required|string|max:12|min:12',
            'voter_id'=>'required|string|max:70|min:10',
            'aadhar_card_parent'=>'required|string|max:12|min:12',
            'driving_licence'=>'nullable|string|max:16',
            'photo'=>'required|mimes:jpg,jpeg,png,webp,svg',
            'aadhaar_photo'=>'required|mimes:jpg,jpeg,png,webp,svg',
            'transfer'=>'nullable|mimes:jpg,jpeg,png,webp,svg',
            'caste_photo'=>'nullable|mimes:jpg,jpeg,png,webp,svg',
            'resident'=>'nullable|mimes:jpg,jpeg,png,webp,svg',
            'birth_certificate_photo'=>'required|mimes:jpg,jpeg,png,webp,svg',
            'ration_card'=>'required|mimes:jpg,jpeg,png,webp,svg',
        ]);
            $photo=$this->saveImage($request['photo']);
            $aadhaar_photo=$this->saveImage($request['aadhaar_photo']);
        if(isset($request['transfer'])){
            $transfer=$this->saveImage($request['transfer']);
        }else{
            $transfer='';
        }
        if(isset($request['caste_photo'])){
            $caste_photo=$this->saveImage($request['caste_photo']);
        }else{
            $caste_photo='';
        }
        if(isset($request['resident'])){
            $resident=$this->saveImage($request['resident']);
        }else{
            $resident='';
        }
            $birth_certificate_photo=$this->saveImage($request['birth_certificate_photo']);
            $ration_card=$this->saveImage($request['ration_card']);
        
        try {
            Admission::create(array_merge($request->except('_token'),[
                'photo'=>$photo,
                'aadhaar_photo'=>$aadhaar_photo,
                'photo'=>$photo,
                'caste_photo'=>$caste_photo,
                'caste_photo'=>$caste_photo,
                'birth_certificate_photo'=>$birth_certificate_photo,
                'ration_card'=>$ration_card,
            ]));

            //Confirmation Notification Mail
            if(!empty($request['email'])){
                $data=[
                    'title'=>"Admission",
                    'name'=>$request['name'],
                ];
                Mail::to($request['email'])->send(new ConfirmationMail($data));
            }

            //Admin Notification Mail
            $data=[
                'name'=>$request['name'],
                'father_name'=>$request['father_name'],
                'mobile1'=>$request['mobile1'],
                'aadhar_card_candidate'=>$request['aadhar_card_candidate'],
                'address'=>$request['address'],
            ];
            Mail::to('<EMAIL>')->send(new AdmissionMail($data));

            return back()->with('success','Admission Form Submitted Successfully!');
        } catch (\Exception $ex) { return $ex;
            return back()->with('error','Admission Form Is Not Submitted!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Admission $admission)
    { 
        return view('app.admission.view',compact('admission'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Admission $admission)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Admission $admission)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Admission $admission)
    {
        try {
            $admission->delete();
            return back()->with('success','Admission Is Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Admission Is Not Deleted!');
        }
    }

    public function admissionUpdate(Request $request)
    {
        try {
            Admission::where('id',$request['id'])->update(['status'=>$request['status']]);
            return 200;
        } catch (\Exception $ex) {
            return $ex;
        }
    }

    public function restore($id){
        try {
            Admission::where('id',$id)->restore();
            return back()->with('success','Admission Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Admission Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        try {
            Admission::where('id',$id)->forceDelete();
            return back()->with('success','Admission Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Admission Is Not Deleted!');
        }
    }

    public function count(){
        $all=Admission::count();
        $approved=Admission::where('status',1)->count();
        $declined=Admission::where('status',2)->count();
        return response()->json([
            'all_count'=>$all,
            'approved_count'=>$approved,
            'declined_count'=>$declined,
        ]);
    }
}
