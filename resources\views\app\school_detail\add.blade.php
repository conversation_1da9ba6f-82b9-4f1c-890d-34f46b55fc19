@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Settings /</span> Update School Details</h4>
    <x-alert></x-alert>
    <div class="card">
        <div class="card-header">
            <h4>Update School Detail</h4>
        </div>
        <form action="{{ route('school-detail-update') }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            <div class="container ">
                <div class="">
                    <label for="" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" name="school_name" @isset($school_name) value="{{ $school_name }}" @endisset class="form-control" maxlength="255" required>
                </div>
                <div class="mt-3">
                    <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                    <textarea class="form-control" required name="address" id="address" required>@isset($address) {{ $address }} @endisset </textarea>
                </div>
                <div class="mt-3">
                    <label for="" class="form-label">Mobile <span class="text-danger">*</span></label>
                    <input type="tel" name="mobile" @isset($mobile) value="{{ $mobile }}" @endisset maxlength="10" minlength="10" class="form-control" required>
                </div>
                <div class="mt-3">
                    <label for="" class="form-label">Email <span class="text-danger">*</span></label>
                    <input type="email" name="email" @isset($email) value="{{ $email }}" maxlength="70" @endisset class="form-control" required>
                </div>
                <div class="mt-3 mb-3">
                    <button class="btn btn-danger me-2" type="reset">Reset</button>
                    <button class="btn btn-primary" type="submit">Submit</button>
                </div>
            </div>
            
        </form>
    </div>
@endsection