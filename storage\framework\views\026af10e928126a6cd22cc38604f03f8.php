<?php
    use App\Models\Setting;
    $logo=Setting::where('key_name','logo')->pluck('value')->first();
    $school_name=Setting::where('key_name','school_name')->pluck('value')->first();
    $address=Setting::where('key_name','address')->pluck('value')->first();
    $mobile=Setting::where('key_name','mobile')->pluck('value')->first();
    $twitter=Setting::where('key_name','twitter')->pluck('value')->first();
    $facebook=Setting::where('key_name','facebook')->pluck('value')->first();
    $linkedin=Setting::where('key_name','linkedin')->pluck('value')->first();
    $youtube=Setting::where('key_name','youtube')->pluck('value')->first();
    $email=Setting::where('key_name','email')->pluck('value')->first();
    $website_name=Setting::where('key_name','website_name')->pluck('value')->first();
    $website_url=Setting::where('key_name','website_url')->pluck('value')->first();
    $favicon=Setting::where('key_name','favicon')->pluck('value')->first();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title><?php if(isset($website_name)): ?> <?php echo e($website_name); ?> <?php endif; ?></title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Canonical Tag-->
    <link rel="canonical" href="<?php echo e(url()->current()); ?>" />
    <!-- Favicon -->
    <?php if(isset($favicon)): ?>
        <link href="<?php echo e(asset('assets/'.$favicon)); ?>" rel="icon">
    <?php endif; ?>

    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@600&family=Lobster+Two:wght@700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="<?php echo e(asset('assets/web/lib/animate/animate.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/web/lib/owlcarousel/assets/owl.carousel.min.css')); ?>" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="<?php echo e(asset('assets/web/css/bootstrap.min.css')); ?>" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="<?php echo e(asset('assets/web/css/style.css')); ?>" rel="stylesheet">

    <style>
        @media (max-width: 992px) { 
			.img_height {
				min-height: 200px !important;
                margin-bottom: 30px; 
			}
		}

        @media (max-width: 768px) {
            .header-carousel .owl-carousel-item {
                position: relative;
                min-height: 200px !important;
            }
        }

        :root{
            --primarycolor:#8f463d;
            --secondarycolor:#d4ddd1;
            --thirdcolor:hsl(215, 20%, 33%);
        }
    </style>
    <style>
          * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #25252b;
        }

        .box {
            position: relative;
            background: repeating-conic-gradient(from var(--a), #ff2770 0%, #ff2770 5%, transparent 5%, transparent 40%, #ff2770 50%);
            animation: animate 4s linear infinite;
            border-radius: 15px;
        }

        @property --a {
            syntax: '<angle>';
            inherits: false;
            initial-value: 0deg; 
        }

        @keyframes animate {
            0% {
                --a: 0deg;
            }
            100% {
                --a: 360deg;
            }
        }

        .box::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: repeating-conic-gradient(from var(--a), #45f3ff 0%, #45f3ff 5%, transparent 5%, transparent 40%, #45f3ff 50%);
        animation: animate 4s linear infinite;
        animation-delay: -1s;
        border-radius: 15px;
        }

        .box::after {
            /* content: ''; */
            position: absolute;
            inset: 8px;
            border-radius: 15px;
            border: 8px solid #25252b;
        }

    </style>
    
    <!-- Alertify js -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/alertify/alertify.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/alertify/alertify_theme.css')); ?>">
    <script src="<?php echo e(asset('assets/alertify/alertify.js')); ?>"></script>
    <?php echo $__env->yieldPushContent('style'); ?>
</head>

<body>
    <div class="container-xxl bg-white p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Navbar Start -->
        <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top px-4 px-lg-5 py-lg-0" style="var(--primarycolor)">
            <a href="<?php echo e(route('index')); ?>" class="navbar-brand">
                <?php if(isset($logo)): ?>
                    <img src="<?php echo e(asset('assets/'.$logo)); ?>" alt="Logo Image" loading="lazy" height="50px;">
                <?php else: ?>
                    <img src="<?php echo e(asset('assets/logo1.png')); ?>" alt="Logo Image" loading="lazy" height="50px;">
                <?php endif; ?>
            </a>
            <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" fdprocessedid="c3nual" aria-expanded="false">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <div class="navbar-nav mx-auto">
                    <a href="<?php echo e(route('index')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('index') ? 'active' : ''); ?>">Home</a>
                    <a href="<?php echo e(route('news-article')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('news-article') ? 'active' : ''); ?>">News</a>
                    <a href="<?php echo e(route('events')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('events') ? 'active' : ''); ?>">Events</a>
                    <a href="<?php echo e(route('syllabuses')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('syllabuses') ? 'active' : ''); ?>">Syllabus</a>
                    <a href="<?php echo e(route('admissions')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('admissions') ? 'active' : ''); ?>">Admission</a>
                    <a href="<?php echo e(route('downloads')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('downloads') ? 'active' : ''); ?>">Downloads</a>
                    <a href="<?php echo e(route('contact-us')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('contact-us') ? 'active' : ''); ?>">Contact</a>
                    <a href="<?php echo e(route('about-us')); ?>" class="nav-item nav-link <?php echo e(request()->routeIs('about-us') ? 'active' : ''); ?>">About Us</a>
                </div>
                
            </div>
        </nav>
        <!-- Navbar End -->

        <?php echo $__env->yieldContent('content'); ?>

        <!-- Footer Start -->
        <div class="container-fluid  text-white-50 footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s" style="background-color:var(--primarycolor);">
            <div class="container py-5">
                <div class="row g-5">
                    <div class="col-lg-4 col-md-6">
                        <h3 class="text-white mb-4">Get In Touch</h3>
                        <p class="mb-2"><i class="fas fa-school me-3"></i><?php if(isset($school_name)): ?> <?php echo e($school_name); ?> <?php endif; ?></p>
                        <p class="mb-2"><i class="fa fa-map-marker-alt me-3 p-1"></i>  <?php if(isset($address)): ?> <?php echo e($address); ?> <?php endif; ?></p>
                        <p class="mb-2"><i class="fa fa-phone-alt me-3"  style="padding-right:4px;"></i><?php if(isset($mobile)): ?> +91 <?php echo e($mobile); ?> <?php endif; ?></p>
                        <p class="mb-2"><i class="fa fa-envelope me-3" style="padding-right:4px;"></i><?php if(isset($email)): ?> <?php echo e($email); ?> <?php endif; ?></p>
                        <div class="d-flex pt-2">
                            <a class="btn btn-outline-light btn-social" href="<?php if(isset($twitter)): ?> <?php echo e($twitter); ?> <?php endif; ?>"><i class="fab fa-twitter"></i></a>
                            <a class="btn btn-outline-light btn-social" href="<?php if(isset($facebook)): ?> <?php echo e($facebook); ?> <?php endif; ?>"><i class="fab fa-facebook-f"></i></a>
                            <a class="btn btn-outline-light btn-social" href="<?php if(isset($youtube)): ?> <?php echo e($youtube); ?> <?php endif; ?>"><i class="fab fa-youtube"></i></a>
                            <a class="btn btn-outline-light btn-social" href="<?php if(isset($linkedin)): ?> <?php echo e($linkedin); ?> <?php endif; ?>"><i class="fab fa-linkedin-in"></i></a>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <h3 class="text-white mb-4">Quick Links</h3>
                        <a class="btn btn-link text-white-50" href="<?php echo e(route('index')); ?>">Home</a>
                        <a class="btn btn-link text-white-50" href="<?php echo e(route('news-article')); ?>">News</a>
                        <a class="btn btn-link text-white-50" href="<?php echo e(route('events')); ?>">Events</a>
                        <a class="btn btn-link text-white-50" href="<?php echo e(route('contact-us')); ?>">Contact Us</a>
                        <a class="btn btn-link text-white-50" href="<?php echo e(route('about-us')); ?>">About Us</a>
                        
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <h3 class="text-white mb-4">Photo Gallery</h3>
                        <div class="row g-2 pt-2">
                            <div class="col-6">
                                <img class="img-fluid rounded bg-light p-1" src="<?php echo e(asset('assets/web/img/classes-1.jpg')); ?>" alt="Gallary Photo" loading="lazy">
                            </div>
                            <div class="col-6">
                                <img class="img-fluid rounded bg-light p-1" src="<?php echo e(asset('assets/web/img/classes-2.jpg')); ?>" alt="Gallary Photo" loading="lazy">
                            </div>
                            
                            
                            
                            
                        </div>
                    </div>
                </div>
            </div>
            <div class="container">
                <div class="copyright">
                    <div class="row">
                        <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                            &copy; <a class="border-bottom" href="<?php if(isset($website_url)): ?> <?php echo e($website_url); ?> <?php endif; ?>"><?php if(isset($website_name)): ?> <?php echo e($website_name); ?> <?php endif; ?></a>, All Right Reserved. <?php echo e(date('Y')); ?>

                        </div>
                        <div class="col-md-6 text-center text-md-end">
                            <div class="footer-menu">
                                <a href="<?php echo e(route('index')); ?>">Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer End -->


        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo e(asset('assets/web/lib/wow/wow.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/web/lib/easing/easing.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/web/lib/waypoints/waypoints.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/web/lib/owlcarousel/owl.carousel.min.js')); ?>"></script>

    <!-- Template Javascript -->
    <script src="<?php echo e(asset('assets/web/js/main.js')); ?>"></script>

    <?php echo $__env->yieldPushContent('script'); ?>
</body>

</html><?php /**PATH C:\xampp\htdocs\school_website\resources\views/layout/web.blade.php ENDPATH**/ ?>