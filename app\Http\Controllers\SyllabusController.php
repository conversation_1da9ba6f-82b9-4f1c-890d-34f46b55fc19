<?php

namespace App\Http\Controllers;

use App\Models\Syllabus;
use Illuminate\Http\Request;
use App\Trait\SaveFile;
use Illuminate\Validation\Rule;

class SyllabusController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $syllabi=syllabus::orderBy('id','desc')->paginate(10);
        return view('app.syllabus.show',compact('syllabi'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    { 
        return view('app.syllabus.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['title'=>'required|string|max:70',
            'pdf'=>'required|mimes:pdf',
            'slug'=>'required|string|max:70',
            'image'=>'required|mimes:jpeg,jpg,png,webp,gif',
            'session'=>'required',
        ]);
        $pdf=$this->savePdf($request['pdf']);
        $image=$this->saveImage($request['image']);
        try {
            Syllabus::create(array_merge($request->except('_token'),['image'=>$image,'pdf'=>$pdf]));
            return to_route('syllabus.index')->with('success','Syllabus Added Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Syllabus Is Not Added!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Syllabus $syllabus)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    { 
        $syllabus=Syllabus::where('id',$id)->first();
        return view('app.syllabus.add',compact('syllabus'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate(['title'=>'required|string|max:70',
            'pdf'=>'nullable|mimes:pdf',
            'slug'=>'required|string|max:70',
            'image'=>'nullable|mimes:jpeg,jpg,png,webp,gif',
            'session'=>'required',
        ]);
        $old_pdf=Syllabus::where('id',$id)->pluck('pdf')->first();
        if(!empty($request['pdf'])){
            $pdf=$this->savePdf($request['pdf']);
            if(file_exists(public_path('pdf/'.$old_pdf))){
                unlink(public_path('pdf/'.$old_pdf));
            }
        }else{
            $pdf=$old_pdf;
        }
        $old_image=Syllabus::where('id',$id)->pluck('image')->first();
        if(!empty($request['image'])){
            $image=$this->saveImage($request['image']);
            if(file_exists(public_path('image/'.$old_image))){
                unlink(public_path('image/'.$old_image));
            }
        }else{
            $image=$old_image;
        }
        
        try {
            Syllabus::where('id',$id)->update(array_merge($request->except('_token','_method'),['image'=>$image,'pdf'=>$pdf]));
            return to_route('syllabus.index')->with('success','Syllabus Updated Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Syllabus Is Not Updated!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {

        try {
            Syllabus::where('id',$id)->delete();
            return back()->with('success','Syllabus Is Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Syllabus Is Not Deleted!');
        }
    }

    public function restore($id){
        try {
            Syllabus::where('id',$id)->restore();
            return back()->with('success','Syllabus Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Syllabus Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        $old_pdf=Syllabus::withTrashed()->where('id',$id)->pluck('pdf')->first();
        $old_image=Syllabus::withTrashed()->where('id',$id)->pluck('image')->first();
        try {
            Syllabus::where('id',$id)->forceDelete();
            if(file_exists(public_path('pdf/'.$old_pdf))){
                unlink(public_path('pdf/'.$old_pdf));
            }
            if(file_exists(public_path('image/'.$old_image))){
                unlink(public_path('image/'.$old_image));
            }
            return back()->with('success','Syllabus Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Syllabus Is Not Deleted!');
        }
    }
}
