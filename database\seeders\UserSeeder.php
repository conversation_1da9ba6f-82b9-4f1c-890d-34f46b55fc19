<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' =>  Hash::make(123415678),
            'user_role'=>'admin',
        ]);

        User::create([
            'name' => '<PERSON><PERSON><PERSON>',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' =>  Hash::make(12345678),
            'user_role'=>'admin',
        ]);
    }
}
