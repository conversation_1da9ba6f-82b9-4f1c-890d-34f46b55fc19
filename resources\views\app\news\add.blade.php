@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">News /</span> 
        @if (isset($news))
            Update
        @else
            Add
        @endif
    </h4>
    <div class="card">
        <div class="card-header">
            @if (isset($news))
                <h4>Update News</h4>
            @else
                <h4>Add News</h4>
            @endif
        </div>
        <x-alert></x-alert>

        @if (isset($news))
            <form action="{{ route('news.update',$news->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
        @else
            <form action="{{ route('news.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
        @endif

                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Title <samp class="text-danger">*</samp></label>
                            <input type="text" name="title" value="@isset($news) {{ $news->title }} @endisset" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date <samp class="text-danger">*</samp></label>
                            <input type="date" name="date" @isset($news) value="{{ $news->date }}" @endisset class="form-control" required>
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Time <samp class="text-danger">*</samp></label>
                            <input type="time" name="time" @isset($news) value="{{ $news->time }}" @endisset class="form-control" required>
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Location <samp class="text-danger">*</samp></label>
                            <input type="text" name="location" value="@isset($news) {{ $news->location }} @endisset" class="form-control" required>
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Short Description</label>
                            <input type="text" name="short_desc" value="@isset($news) {{ $news->short_desc }} @endisset" class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="0" @isset($news) {{ $news->status == "0" ? 'selected' : '' }} @endisset>Show</option>
                                <option value="1" @isset($news) {{ $news->status == "1" ? 'selected' : '' }} @endisset>Hide</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Long Description <samp class="text-danger">*</samp></label>
                        <textarea  class="form-control" name="long_desc" id="long_desc" cols="30" rows="3" required>@isset($news) {{ $news->long_desc }} @endisset</textarea>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Image <samp class="text-danger">*</samp>(Recommended image size 300*300 pixels)</label>
                        <input type="file" name="image"  class="form-control mb-3" accept="image/*" @empty($news->image) required @endempty>
                        @isset($news)
                            <img src="{{ asset('public/image/'.$news->image) }}" alt="News Image" width="100px" height="100px"> 
                        @endisset
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </form>
    </div>
@endsection