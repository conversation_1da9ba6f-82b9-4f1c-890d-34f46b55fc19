@extends('layout.web')

@push('style')
    <style>
        #gallery {
            display: flex;
            flex-direction: column-reverse;
            min-height: 100vh;
            margin: 0;
            & > svg {
                display: none;
            }
        }

        main {
        width: 100%;
        margin: auto;
        padding: 18px;
        font-family: "Trebuchet MS", sans-serif;
        line-height: 1.33em;
        box-sizing: border-box;

        a {
            text-decoration: none;
        }

        & * {
            margin: 0;
            color: #ffffffe0;
        }

        &.shrink {
            animation-name: shrink;
            animation-fill-mode: forwards;
            animation-duration: 2s;
        }
        &.expand {
            animation-duration: 2s;
            animation-fill-mode: forwards;
            animation-name: expand;
        }

        &.delay {
            animation-delay: 1.3s;
        }

        p.info span::first-letter {
            font-size: 180%;
            font-family: serif;
        }
        }

        aside {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        height: 66px;
        padding-top: 3vh;
        position: relative;

        button {
            background-color: white;
            border: 3px dotted #d1d4d9;
            line-height: 20px;
            height: min-content;
            height: 35px;
            padding-inline: 10px;
            box-sizing: content-box;
            cursor: pointer;
            color: #077d8a;
            font-size: 15px;
            letter-spacing: -0.025em;
            span {
            font-size: 115%;
            }
            &::first-letter {
            color: rgb(152, 15, 113);
            font-weight: 600;
            }

            &:hover {
            border: 6px dashed #d4ddd1;
            margin: -3px;
            transition: 0.3s all;
            }

            &.dark {
            filter: invert(1);
            color: black;
            border: 3px dotted #29222d;
            &:hover {
                border: 6px dashed #29222d;
            }
            }

            &.efx {
            transform: skewX(-8deg);
            }
            &.efx.zoomFx {
            transform: scaleY(1.13);
            }
        }
        }

        @media (max-width: 808px) {
        aside {
            height: 122px;
        }
        }

        .gridOverflow.go-masonry {
        .go_gridItem .go_caption {
            display: none;
        }
        .go_gridItem:nth-child(4) .go_caption {
            display: block;
            &:first-line {
            float: left;
            color: transparent;
            line-height: 2px;
            }
        }
        }

        @keyframes shrink {
        from {
            max-width: 880px;
        }
        to {
            max-width: 500px;
        }
        }

        @keyframes expand {
        from {
            max-width: 500px;
        }
        to {
            max-width: 880px;
        }
        }

    </style>
@endpush

@section('content')
    <!-- Carousel Start -->
    <div class="container-fluid p-0 mb-5">
        <div class="owl-carousel header-carousel position-relative">
            @foreach ($banners as $banner)
                <div class="owl-carousel-item position-relative">
                    <img class="img-fluid img_height h-100 w-100" src="{{ asset('public/image/'.$banner->image) }}" alt="Hero Image" loading="lazy">
                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: rgba(0, 0, 0, .2);">
                        <div class="container">
                            <div class="row justify-content-start">
                                <div class="col-10 col-lg-8">
                                    <h3 class="display-2 text-white animated slideInDown mb-4">{{ $banner->title }}</h3>
                                    <p class="fs-5 fw-medium text-white mb-4 pb-2">{{ $banner->description }}</p> 
                                    {{-- <a href="{{ route('about-us') }}" class="btn btn-primary rounded-pill py-sm-3 px-sm-5 me-3 animated slideInLeft">Learn More</a> --}}
                                    {{-- <a href="" class="btn btn-dark rounded-pill py-sm-3 px-sm-5 animated slideInRight">Our Classes</a> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach 
        </div>
    </div>
    <!-- Carousel End -->

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <marquee><h3 class="text-white">* Aims to build a strong foundation for lifelong learning and personal growth.</h3></marquee>
        <a href="{{ route('admissions') }}"><marquee><h3 class="text-white">*  If you’re looking to "Admission" section on your school’s website, <span class="text-danger">click here</span> </h3></marquee></a>
        <a href="{{ route('contact-us') }}"><marquee><h3 class="text-white">* If you’re looking to "Enquiry" section on your school’s website, <span class="text-danger">click here</span> </h3></marquee></a>
    </div>

    <!-- Gallary-->
    {{-- <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <div class="container ">
            <div class="row g-5 align-items-center ">
                <div class="col-lg-12 col-md-12">
                    <h2 class="text-white mb-4">Photo Gallery</h2>
                    <div class="row g-2 pt-2">
                        @foreach ($galleries as $row)
                            <div class="col-lg-3 col-4">
                                <img class="img-fluid rounded bg-light p-1" src="{{ asset('public/image/'.$row->image) }}" alt="Gallary Photo" loading="lazy">
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div> --}}

    <div style="color: #ffffffe0; background: var(--secondarycolor)" id="gallery" class="py-3">
        <!-- ~~~~ Grid Overflow CSS library, it creates responsive grid layout for thumbnails ~~~~  -->
        <link href="https://cdn.jsdelivr.net/npm/grid-overflow@1/src/GridOverflow3D.min.css" rel="stylesheet">
        <!-- Story Show Gallery on click opens images into full screen lightbox -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/story-show-gallery@3/dist/ssg.min.css" />
        <base href="https://roman-flossler.github.io/StoryShowGallery/photos/"  />
    
        <style>
        /* configuration of the Grid Overflow  */
        .gridOverflow {
            --gridGap: 1px;
            --itemMinWidth: 190px;
            --itemAspectRatio: 1;
            --itemRounding: 6px;
            --linkActionIcon: "⫸";
        }
    
        .gridOverflow.go-masonry {
            --gridGap: 8px;
            --itemMinWidth: 30%;
            --itemRounding: 25% 88% 10% 88% / 18% 9px 15% 9px;
            --linkActionIcon: "»";
            --masonryItemHeight: 193px;
        }
    
        @media (max-width: 600px) {
            .gridOverflow.go-masonry {
            --itemMinWidth: 40%;
            --masonryItemHeight: 166px;
            }
        }
    
        @container fenceBox (max-width: 600px) {
            .gridOverflow.go-masonry {
            --itemMinWidth: 40%;
            --masonryItemHeight: 166px;
            }
        }
        </style>
        <main id="fenceBox" class="expand delay" style="max-width: 500px; container: fenceBox / inline-size">
        <!-- parent grid element - gridOverflow class creates responsive square grid from grid's direct children (items) -->
        <!-- go-3Dfx class adds to each grid item 3D animated effect on mouse over  -->
        <!-- go-actionIcon class adds to top right corner of grid items some symbol, but only if grid item is <a> tag  -->
        <!-- go-masonry class switch the layout into masonry mode -->
    
        <div class="gridOverflow go-masonry go-3Dfx go-actionIcon ssg fs" id="grid">
        
            @foreach ($galleries as $row)
            <a href="{{ asset('public/image/'.$row->image) }}" class="go_gridItem go_gridItem-panorama go_gridItem-centered">
                <img src="{{ asset('public/image/'.$row->image) }}" alt="Gallary Photo" loading="lazy" />
            </a>
            @endforeach
    
        </div>
        </main>
    
        <!-- Story Show Gallery script and configuration -->
        <script src="https://cdn.jsdelivr.net/npm/story-show-gallery@3/dist/ssg.min.js"></script>
        <script>
        SSG.cfg.globalAuthorCaption =
            "<a href='https://roman-flossler.github.io/StoryShowGallery' >Story Show Gallery</a>";
        SSG.cfg.watermarkText = "=^..^=";
        SSG.cfg.watermarkFontSize = 19;
        SSG.cfg.watermarkOffsetX = 98;
        SSG.cfg.watermarkOffsetY = 2;
        SSG.cfg.watermarkOpacity = 0.77;
        SSG.cfg.theme = "light";
        </script>
    
        <!-- buttons for playing with the gallery features  -->
        {{-- <aside>
        <button onclick="toggleThumbnailsLayout()"><span>᎒᎒᎒</span>&ensp;masonry / grid</button>
        ___
        <button onclick="showResponsivity()"><span>↹</span>&ensp;show responsivity</button>
        ___
        <button class="efx" onclick="toggleHoverEffect(this)">✴&ensp;tilt / zoom :hover</button>
    
        <span style="width: 39px"></span>
    
        <button onClick="toggleGalleryTheme(this)">◑&ensp;theme: LIGHT</button>
        ___
        <button id="ssgrun" onClick="runStoryShowGallery()">▶&ensp;Gallery run</button>
        </aside> --}}
    
    </div>
    <!-- End Gallary-->

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <marquee direction="up"><h3 class="text-white">Offers a nurturing and supportive environment for social and emotional development.</h3></marquee>
    </div>

    <!-- Facilities Start -->
    <div class="container-xxl py-3 mt-3 mb-5">
        <div class="container">
            <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
                <h2 class="mb-3">School Facilities</h2>
                <p>
                    A school facility encompasses the physical buildings and grounds that make up a school, providing the necessary infrastructure for educational activities. Key components of a school facility typically include:</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="facility-item">
                        <div class="facility-icon bg-primary">
                            <span class="bg-primary"></span>
                            <i class="fa fa-bus-alt fa-3x text-primary"></i>
                            <span class="bg-primary"></span>
                        </div>
                        <div class="facility-text bg-primary">
                            <h3 class="text-primary mb-3">School Bus</h3>
                            <p class="mb-0">Safety features such as flashing lights and stop signs</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.3s">
                    <div class="facility-item">
                        <div class="facility-icon bg-success">
                            <span class="bg-success"></span>
                            <i class="fa fa-futbol fa-3x text-success"></i>
                            <span class="bg-success"></span>
                        </div>
                        <div class="facility-text bg-success">
                            <h3 class="text-success mb-3">Playground</h3>
                            <p class="mb-0">Children to play and engage in physical activities and social interaction.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.5s">
                    <div class="facility-item">
                        <div class="facility-icon bg-warning">
                            <span class="bg-warning"></span>
                            <i class="fa fa-home fa-3x text-warning"></i>
                            <span class="bg-warning"></span>
                        </div>
                        <div class="facility-text bg-warning">
                            <h3 class="text-warning mb-3">Class Rooms</h3>
                            <p class="mb-0">Designed to facilitate learning and engagement through structured lessons, discussions, and activities.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.7s">
                    <div class="facility-item">
                        <div class="facility-icon bg-info">
                            <span class="bg-info"></span>
                            <i class="fa fa-chalkboard-teacher fa-3x text-info"></i>
                            <span class="bg-info"></span>
                        </div>
                        <div class="facility-text bg-info">
                            <h3 class="text-info mb-3">Positive Learning</h3>
                            <p class="mb-0">It involves creating a supportive and encouraging environment </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Facilities End -->


    <!-- About Start -->
    {{-- <div class="container-xxl py-5">
        <div class="container">
            <div class="row g-5 align-items-center">
                <div class="col-lg-6 wow fadeInUp" data-wow-delay="0.1s">
                    <h1 class="mb-4">Learn More About Our Work And Our Cultural Activities</h1>
                    <p>Tempor erat elitr rebum at clita. Diam dolor diam ipsum sit. Aliqu diam amet diam et eos. Clita erat ipsum et lorem et sit, sed stet lorem sit clita duo justo magna dolore erat amet</p>
                    <p class="mb-4">Stet no et lorem dolor et diam, amet duo ut dolore vero eos. No stet est diam rebum amet diam ipsum. Clita clita labore, dolor duo nonumy clita sit at, sed sit sanctus dolor eos, ipsum labore duo duo sit no sea diam. Et dolor et kasd ea. Eirmod diam at dolor est vero nonumy magna.</p>
                    <div class="row g-4 align-items-center">
                        <div class="col-sm-6">
                            <a class="btn btn-primary rounded-pill py-3 px-5" href="">Read More</a>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <img class="rounded-circle flex-shrink-0" src="img/user.jpg" alt="" style="width: 45px; height: 45px;">
                                <div class="ms-3">
                                    <h6 class="text-primary mb-1">Jhon Doe</h6>
                                    <small>CEO & Founder</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 about-img wow fadeInUp" data-wow-delay="0.5s">
                    <div class="row">
                        <div class="col-12 text-center">
                            <img class="img-fluid w-75 rounded-circle bg-light p-3" src="img/about-1.jpg" alt="">
                        </div>
                        <div class="col-6 text-start" style="margin-top: -150px;">
                            <img class="img-fluid w-100 rounded-circle bg-light p-3" src="img/about-2.jpg" alt="">
                        </div>
                        <div class="col-6 text-end" style="margin-top: -150px;">
                            <img class="img-fluid w-100 rounded-circle bg-light p-3" src="img/about-3.jpg" alt="">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> --}}
    <!-- About End -->

    <section class="text-block pb-xs-30 pb-sm-50 pb-md-60 on-scroll incep show-scroll mb-3" style="background-color: var(--secondarycolor)">
        <div class="container">
            <div class="row align-items-sm-center align-items-lg-center">
                
                <div class="col-md-6 on-scroll fade-up fade-scale pt-xs-35 pt-md-0 show-scroll mt-3">
                    <div class="block-header mb-xs-25 mb-md-25 text-center">
                        <p class="mb-xs-15 mb-lg-8">Where fun and learning come together</p>
                        <h1 class="mb-xs-25 mb-md-25 inzz"><span>India’s</span> Play School</h1>
                    </div>
                    <p style="text-align:justify" class="mb-xs-20 mb-md-30 ms-3"><b>Fun:</b> Activities are designed to capture interest and make the experience enjoyable. This could involve games, interactive lessons, or creative projects. </p>
                        <p style="text-align:justify" class="mb-xs-20 mb-md-30 ms-3"><b>Learning:</b> Educational content is embedded within these activities, so while participants are having fun, they are also gaining knowledge or developing skills. </p>
                        <p style="text-align:justify" class="mb-xs-20 mb-md-30 ms-3"><b>Educational Apps/Games:</b> Platforms like Kahoot! or Duolingo make learning fun by incorporating game-like elements. </p>
                        <p style="text-align:justify" class="mb-xs-20 mb-md-30 ms-3"><b>Interactive Museums:</b> Science centers or children’s museums often have hands-on exhibits that make learning an active and enjoyable process. </p>
                        <p style="text-align:justify" class="mb-xs-20 mb-md-30 ms-3"><b>Classroom Activities:</b> Teachers might use group projects, educational games, or creative assignments to make lessons more engaging.</p>
                </div>
                <div class="col-md-6 on-scroll fade-left fade-scale imgzz show-scroll">
                    <div id="carouselExampleSlidesOnly" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            <div class="carousel-item active"><center><img src="{{ asset('assets/web/img/classes-1.jpg') }}" class="d-block w-50" alt="..."></center></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <marquee><h3 class="text-white">Focuses on basic subjects like reading, writing, math, and science.</h3></marquee>
    </div>

    <!--Latest News -->
    <div class="container-xxl py-3">
        <div class="container">
            <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
                <h2 class="mb-3">Latest News</h2>
            </div>
            @foreach ($news as $row)
                <div class="card flex-md-row mb-4 box-shadow h-md-250 bg-light box">
                    <img class="card-img-right flex-auto d-md-none img-fluid" src="{{ asset('public/image/'.$row->image) }}" alt="Program Image" data-holder-rendered="true" style="width: 300px; height: 300px;">
                    <div class="card-body d-flex flex-column align-items-center">
                        <h3 class="mb-0" style="display: block; text-align: center !important;">
                            {{ $row->title }}
                        </h3><br>
                        <div class="mb-1">Date: {{ $row->date }} <span>Time:  {{ $row->time }}</span></div>
                        
                        <div class="mb-1">Location: {{ $row->location }}</div>
                        @isset($row->short_desc)<p>{{ $row->short_desc }}</p>@endisset
                        <p class="card-text text-justify">{{ $row->long_desc }}</p>
                    </div>
                    <img class="card-img-right flex-auto d-none  d-md-block img-fluid" src="{{ asset('public/image/'.$row->image) }}" alt="Program Image" data-holder-rendered="true" style="width: 300px; height: 300px;">
                </div>
            @endforeach
        </div>
    </div>
    <!--End Latest News -->

    <div class="container-xxl py-3"  style="background-color: var(--secondarycolor)">
        <div class="container">
            @if (!empty($logo))
                <div class="text-center">
                    <img src="{{ asset('assets/'.$logo) }}" alt="Logo Image" loading="lazy" height="100px;">
                    <h3 class="mt-3">INFRASTRUCTURE</h3>
                </div>
            @endif
            <p style="text-align:justify"> {{ isset($website_name) ? $website_name : '' }} the preschool segment as India’s  play school brand. Our dedicated and well-experienced educators nurture the holistic growth and development of preschool kids with innovative teaching methodologies and advanced learning technologies.</p>
            
                <p  style="text-align:justify">{{ isset($website_name) ? $website_name : '' }}, Bilaspur is located. A sound palatial infrastructure has been designed to impart quality education with all amenities to all classes. The school has a vast and varied playground to promote all kinds of sports activities. The school also possesses well equipped laboratories for each of the practical subjects from science stream along with multiple high-tech computer practical rooms well maintained with latest computers and devices. The school also has Music and Art rooms for grooming young talents. The school is divided into four main blocks. Each block has been designed specifically for the classes which are conducted there.</p>
        </div>
    </div>

    <!-- Upcomming Event -->
    <div class="container-xxl py-3">
        <div class="container">
            <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
                <h2 class="mb-3"> Upcomming Event</h2>
            </div>
            @foreach ($events as $event)
                <div class="card flex-md-row mb-4 box-shadow h-md-250 bg-light box">
                    <img class="card-img-right flex-auto d-md-none img-fluid" src="{{ asset('public/image/'.$event->image) }}" alt="Program Image" data-holder-rendered="true" style="width: 300px; height: 300px;">
                    <div class="card-body d-flex flex-column align-items-center">
                        <h3 class="mb-0" style="display: block; text-align: center !important;">
                            {{ $event->title }}
                        </h3><br>
                        <div class="mb-1">Date: {{ $event->date }} <span>Time:  {{ $event->time }}</span></div>
                        
                        <div class="mb-1">Location: {{ $event->location }}</div>
                        @isset($event->short_desc)<p>{{ $event->short_desc }}</p>@endisset
                        <p class="card-text text-justify">{{ $event->long_desc }}</p>
                    </div>
                    <img class="card-img-right flex-auto d-none  d-md-block img-fluid" src="{{ asset('public/image/'.$event->image) }}" alt="Program Image" data-holder-rendered="true" style="width: 300px; height: 300px;">
                </div>
            @endforeach
        </div>
    </div>
    <!--End Upcomming Event -->

    <div class="container-xxl py-3" style="background-color: var(--secondarycolor)">
        <div class="container">
            <div class="text-center">
                <h2 class="mb-3"> Our Courses</h2>
            </div>
            <br>
            <h3>Pre Primary Level:: Class LKG to Class KG2</h3>
            <li> Child-led activities with toys, puzzles, and games.</li>
            <li>Teacher-guided learning sessions focusing on specific skills.</li>
            <li>Visual aids like charts, posters, and displays of children's work.</li>
            <li> Watching for developmental milestones and providing support.</li>
            <li>ntroduction to numbers, letters, shapes, and colors.</li>
            <br>
            <h3>Primary Level:: Class I to Class V</h3>
            <li>A thematic approach is designed to stimulate the children through various interactive sessions and activities
            <li>Extensive use of teaching aids to supplement classroom teaching</li>
            <li>The project works in class calls for mass participation</li>
            <li>Field trips and excursions make learning fun</li>
            <li>Use of the newspaper articles, quizzes, crosswords, and other learning tools enhance general awareness</li>
        </div>
    </div>


    <!-- Team Start -->
    {{-- <div class="container-xxl py-5">
        <div class="container">
            <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
                <h1 class="mb-3">Popular Teachers</h1>
                <p>Eirmod sed ipsum dolor sit rebum labore magna erat. Tempor ut dolore lorem kasd vero ipsum sit
                    eirmod sit. Ipsum diam justo sed rebum vero dolor duo.</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="team-item position-relative">
                        <img class="img-fluid rounded-circle w-75" src="img/team-1.jpg" alt="">
                        <div class="team-text">
                            <h3>Full Name</h3>
                            <p>Designation</p>
                            <div class="d-flex align-items-center">
                                <a class="btn btn-square btn-primary mx-1" href=""><i class="fab fa-facebook-f"></i></a>
                                <a class="btn btn-square btn-primary  mx-1" href=""><i class="fab fa-twitter"></i></a>
                                <a class="btn btn-square btn-primary  mx-1" href=""><i class="fab fa-instagram"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.3s">
                    <div class="team-item position-relative">
                        <img class="img-fluid rounded-circle w-75" src="img/team-2.jpg" alt="">
                        <div class="team-text">
                            <h3>Full Name</h3>
                            <p>Designation</p>
                            <div class="d-flex align-items-center">
                                <a class="btn btn-square btn-primary mx-1" href=""><i class="fab fa-facebook-f"></i></a>
                                <a class="btn btn-square btn-primary  mx-1" href=""><i class="fab fa-twitter"></i></a>
                                <a class="btn btn-square btn-primary  mx-1" href=""><i class="fab fa-instagram"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.5s">
                    <div class="team-item position-relative">
                        <img class="img-fluid rounded-circle w-75" src="img/team-3.jpg" alt="">
                        <div class="team-text">
                            <h3>Full Name</h3>
                            <p>Designation</p>
                            <div class="d-flex align-items-center">
                                <a class="btn btn-square btn-primary mx-1" href=""><i class="fab fa-facebook-f"></i></a>
                                <a class="btn btn-square btn-primary  mx-1" href=""><i class="fab fa-twitter"></i></a>
                                <a class="btn btn-square btn-primary  mx-1" href=""><i class="fab fa-instagram"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> --}}
    <!-- Team End -->


    <!-- Testimonial Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
                <h2 class="mb-3">Our Parents Say!</h2>
                <p>It sounds like you're interested in how parents view tech-driven learning. Parents often have a mix of perspectives on this topic.</p>
            </div>
            <div class="owl-carousel testimonial-carousel wow fadeInUp" data-wow-delay="0.1s">
                <div class="testimonial-item rounded p-5 bg-light">
                    <p class="fs-5">Sharma questions the effectiveness of some tech-driven tools. </p>
                    <div class="d-flex align-items-center bg-white me-n5" style="border-radius: 50px 0 0 50px;">
                        
                        <div class="ps-3">
                            <h3 class="mb-1">Prachi Sharma</h3>
                            <span>Teacher</span>
                        </div>
                        
                    </div>
                </div>
                <div class="testimonial-item rounded p-5 bg-light">
                    <p class="fs-5"> Yadaw is worried about the digital divide and the unequal access to technology. </p>
                    <div class="d-flex align-items-center bg-white me-n5" style="border-radius: 50px 0 0 50px;">
                        
                        <div class="ps-3">
                            <h3 class="mb-1">Neelkanth Yadaw</h3>
                            <span>Manager</span>
                        </div>
                        
                    </div>
                </div>
                <div class="testimonial-item rounded p-5 bg-light">
                    <p class="fs-5"> Nishad embraces technology and is enthusiastic about its potential to make learning more engaging and interactive. </p>
                    <div class="d-flex align-items-center bg-white me-n5" style="border-radius: 50px 0 0 50px;">
                        
                        <div class="ps-3">
                            <h3 class="mb-1">Rohit Nishad</h3>
                            <span>Doctor</span>
                        </div>
                        
                    </div>
                </div>
                <div class="testimonial-item rounded p-5 bg-light">
                    <p class="fs-5">Soni views technology as a valuable tool for developing essential skills such as digital literacy, problem-solving, and critical thinking. </p>
                    <div class="d-flex align-items-center bg-white me-n5" style="border-radius: 50px 0 0 50px;">
                        
                        <div class="ps-3">
                            <h3 class="mb-1">Mukesh Soni</h3>
                            <span>Cassier</span>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <!-- Testimonial End -->

    <div class="container-xxl py-3" style="background-color: var(--secondarycolor)">
        <div class="container">
            <div class="text-center">
                <h2 class="mb-3"> Teaching Methodology</h2>
            </div>
            <h3>Play-Based Learning:</h3>
            <li>Learning through play, fostering creativity and exploration.</li>
            <li>Activities like role-playing, building blocks, and interactive games.</li>
            <br>
            <h3>Direct Instruction:</h3>
            <li>Teacher-led approach with clear, structured lessons.</li>
            <li>Focus on basic academic skills, like reading and math, through repetition and practice.</li>
            <li>Emphasis on clear expectations and consistent feedback.</li>
            <br>
            <h3>Experiential Learning:</h3>
            <li>Learning through experience and hands-on activities.</li>
            <li>Field trips, science experiments, and real-life problem-solving tasks.</li>
            <li>Focus on engaging all senses and reflecting on experiences.</li>
            <br>
            <h3>Storytelling and Narration:</h3>
            <li>Teaching through stories and narratives to develop language skills.</li>
            <li>Encouraging imagination, listening, and comprehension.</li>
            <li>Using puppets, picture books, and oral storytelling.</li>
        </div>
    </div>

    <div class="container-xxl py-3">
        <div class="container">
            <div class="text-center">
                <h2 class="mb-3"> Our Mission</h2>
            </div>
            <p>Our mission is to provide a nurturing and inclusive environment where children are inspired to explore their passions, develop critical thinking skills, and grow into responsible, compassionate global citizens. We strive to offer a well-rounded education that emphasizes academic excellence, creativity, and character development, preparing our students to thrive in an ever-changing world.</p>
            <p>These can be adapted based on your specific goals, values, and the unique culture of your school. Let me know if you'd like more tailored suggestions!</p>
            <p>This emphasizes the school's commitment to creating a safe, supportive, and welcoming atmosphere for all students. A nurturing environment means that each child feels valued, cared for, and encouraged, while an inclusive environment ensures that every child, regardless of background, ability, or identity, is given equal opportunities to succeed.</p>
            <p>This part of the mission focuses on encouraging students to discover and pursue their individual interests and talents. The school seeks to ignite curiosity and motivation in students, allowing them to find and develop what they are truly passionate about.
            </p>
        </div>
    </div>

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <marquee direction="down"><h3 class="text-white">Incorporates creative activities like arts, music, and physical education.</h3></marquee>
    </div>

    <div class="container-xxl py-3" style="background-color: var(--secondarycolor)">
        <div class="container">
            <div class="text-center">
                <h2 class="mb-3">Our Vision</h2>
            </div>
            <p>This part of the vision emphasizes your commitment to helping every child realize their unique abilities and talents. The goal is to create an environment where children are encouraged and supported to explore and develop their skills, ensuring that they can reach their personal best in all aspects of life.</p>
            <p>Holistic education refers to a teaching approach that goes beyond academics to include emotional, social, physical, and ethical development. This means the school is dedicated to nurturing the whole child, recognizing that academic success is closely tied to other aspects of personal growth.</p>
            <p>The vision includes cultivating a passion for learning that extends beyond the classroom. The school aims to inspire curiosity and a desire for knowledge that continues throughout the students' lives, preparing them to adapt and thrive in an ever-changing world.</p>
            <p>Compassion highlights the importance of empathy, kindness, and understanding, which are crucial for personal and social well-being. Innovation points to the encouragement of creative thinking and problem-solving, ensuring that students are not just consumers of knowledge but also creators of new ideas and solutions.</p>
        </div>
    </div>
@endsection

@push('script')
    <script>
        // buttons functionality - mostly toggling of CSS classes

        toggleThumbnailsLayout = function () {
        document.getElementById("grid").classList.toggle("go-masonry");
        };

        function showResponsivity() {
        ["expand", "shrink"].forEach((cls) => {
            document.getElementById("fenceBox").classList.toggle(cls);
        });
        }

        function toggleHoverEffect(that) {
        document.getElementById("grid").classList.toggle("go-zoomFx");
        that.classList.toggle("zoomFx");
        }

        function toggleGalleryTheme(that) {
        SSG.cfg.theme = SSG.cfg.theme === "light" ? "dark" : "light";
        that.innerText = "◑ theme: " + SSG.cfg.theme.toUpperCase();
        document.getElementById("ssgrun").classList.toggle("dark");
        }

        function runStoryShowGallery() {
        SSG.run({ fs: false, initImgName: ["klaksvik-sheeps", 1] });
        }

        setTimeout(() => {
        document.getElementById("fenceBox").classList.remove("delay");
        }, 3333);

    </script>
@endpush