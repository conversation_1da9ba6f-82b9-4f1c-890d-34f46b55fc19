@extends('layout.app')
@section('content')
    <div class="d-flex justify-content-between">
        <div >
            <h4 class="py-3 mb-4"><span class="text-muted fw-light">Recycle Bin /</span> List</h4>
        </div>
    </div>
    <x-alert></x-alert>
    <div class="card">
        <div class="table-responsive text-nowrap">
            <table class="table">
                <thead class="table-light">
                    <tr>
                        <th>Action</th>
                        <th>Type</th>
                        <th>Key Name</th>
                        <th>Value</th>
                        <th>Deleted Date & Time</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @if(Auth::user() && Auth::user()->user_role=="admin" || Auth::user()->user_role=="superadmin")
                        {{-- banner --}}
                        @foreach ($banners as $row)
                            <tr>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                        <a class="dropdown-item waves-effect" href="{{ route('banner-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                            <form action="{{ route('banner.permanently-destroy',$row->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                                <td>Banner</td>
                                <td>Image</td>
                                <td><img src="{{ asset('public/image/'.$row->image) }}" alt="Banner Image" width="100px;" height="100px;" loading="lazy"></td>
                                <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                            </tr>
                        @endforeach

                        {{-- gallery --}}
                        @foreach ($galleries as $row)
                            <tr>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                        <a class="dropdown-item waves-effect" href="{{ route('gallery-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                            <form action="{{ route('gallery.permanently-destroy',$row->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                                <td>gallery</td>
                                <td>Image</td>
                                <td><img src="{{ asset('public/image/'.$row->image) }}" alt="Gallery Image" width="100px;" height="100px;" loading="lazy"></td>
                                <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                            </tr>
                        @endforeach
                    @endif
                    @foreach ($news as $row)
                        <tr>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                    <i class="mdi mdi-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                    <a class="dropdown-item waves-effect" href="{{ route('news-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                        <form action="{{ route('news.permanently-destroy',$row->id) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </td>
                            <td>News</td>
                            <td>Title</td>
                            <td>{{ $row->title }}</td>
                            <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                        </tr>
                    @endforeach
                    @foreach ($events as $row)
                        <tr>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                    <i class="mdi mdi-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                    <a class="dropdown-item waves-effect" href="{{ route('event-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                        <form action="{{ route('event.permanently-destroy',$row->id) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </td>
                            <td>Event</td>
                            <td>Title</td>
                            <td>{{ $row->title }}</td>
                            <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                        </tr>
                    @endforeach
                    @if(Auth::user() && Auth::user()->user_role=="admin" || Auth::user()->user_role=="superadmin")
                        @foreach ($syllabi as $row)
                            <tr>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                        <a class="dropdown-item waves-effect" href="{{ route('syllabus-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                            <form action="{{ route('syllabus.permanently-destroy',$row->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                                <td>Syllabus</td>
                                <td>Title</td>
                                <td>{{ $row->title }}</td>
                                <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                            </tr>
                        @endforeach
                        @foreach ($downloads as $row)
                            <tr>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                        <a class="dropdown-item waves-effect" href="{{ route('download-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                            <form action="{{ route('download.permanently-destroy',$row->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                                <td>Download</td>
                                <td>Title</td>
                                <td>{{ $row->title }}</td>
                                <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                            </tr>
                        @endforeach
                        @foreach ($admissions as $row)
                            <tr>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                        <a class="dropdown-item waves-effect" href="{{ route('admission-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                            <form action="{{ route('admission.permanently-destroy',$row->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                                <td>Admission</td>
                                <td>Name</td>
                                <td>{{ $row->name }}</td>
                                <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                            </tr>
                        @endforeach
                        @foreach ($enquiries as $row)
                            <tr>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                        <a class="dropdown-item waves-effect" href="{{ route('enquiry-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                            <form action="{{ route('enquiry.permanently-destroy',$row->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                                <td>Enquiry</td>
                                <td>Name</td>
                                <td>{{ $row->name }}</td>
                                <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                            </tr>
                        @endforeach
                        @foreach ($staffs as $row)
                            <tr>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                        <a class="dropdown-item waves-effect" href="{{ route('staff-restore',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Restore</a>
                                            <form action="{{ route('staff.permanently-destroy',$row->id) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button  class="dropdown-item waves-effect" type="submit"><i class="mdi mdi-trash-can-outline me-1"></i> Permanently Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                                <td>Staff</td>
                                <td>Name</td>
                                <td>{{ $row->name }}</td>
                                <td>{{ \Carbon\Carbon::parse($row->deleted_at)->format('d-M-Y h:i A') }}</td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>
    </div>
@endsection