<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Validation\Rule;
use Auth;
use Hash;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $staffs=User::where('user_role','staff')->orderBy('id','desc');

        if (isset($request->name)) { 
            $staffs = $staffs->where('name', 'LIKE',"%".$request->name."%");
        }
        
        $staffs = $staffs->paginate(10);
        
        if ($request->ajax()) {
            $view = view('app.staff.staff_data', compact('staffs'))->render();
            $pagination = view('app.staff.pagination', compact('staffs'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('app.staff.show',compact('staffs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('app.staff.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['name'=>'required|string|max:70',
            'email'=>'required|email|unique:users,email|max:70',
            'password'=>'required|min:8|max:12|confirmed'
        ]);

        try {
            User::create([
                'name'=>$request['name'],
                'email'=>$request['email'],
                'password'=>Hash::make($request['password']),
                'user_role'=>'staff',
                'status'=>$request['status'],
            ]);
            return to_route('staff.index')->with('success','Staff Added Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Staff Is Not Added!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $staff=User::where('id',$id)->first();
        return view('app.staff.add',compact('staff'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate(['name'=>'required|string|max:70',
            'email'=>[
                'required','email','max:70',
                Rule::unique('users')->ignore($id),
            ],
            'password'=>'nullable|min:8|max:12|confirmed'
        ]);
        if(empty($request['password'])){
            $password=User::where('id',$id)->pluck('password')->first();
        }else{
            $password=Hash::make($request['password']);
        }

        try {
            User::where('id',$id)->update([
                'name'=>$request['name'],
                'email'=>$request['email'],
                'password'=>$password,
                'status'=>$request['status'],
            ]);
            return to_route('staff.index')->with('success','Staff Updated Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Staff Is Not Updated!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            User::where('id',$id)->delete();
            return back()->with('success','Staff Is Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Staff Is Not Deleted!');
        }
    }

    public function restore($id){
        try {
            User::where('id',$id)->restore();
            return back()->with('success','Staff Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Staff Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        try {
            User::where('id',$id)->forceDelete();
            return back()->with('success','Staff Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Staff Is Not Deleted!');
        }
    }
}
