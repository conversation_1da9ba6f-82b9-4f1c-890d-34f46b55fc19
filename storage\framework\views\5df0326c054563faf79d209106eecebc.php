<?php $__env->startSection('content'); ?>
    <!-- Page Header End -->
    <div class="container-xxl py-5 page-header position-relative mb-5">
        <div class="container py-5">
            <h1 class="display-2 text-white animated slideInDown mb-4">Events</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('index')); ?>">Home</a></li>
                    <li class="breadcrumb-item text-white active" aria-current="page">Events</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <marquee><h3 class="text-white">Societal engagement programs are initiatives designed to connect schools with the broader community, fostering mutual support and collaboration. </h3></marquee>
    </div>
    
    <div class="container-xxl py-3">
        <div class="container m-3">
            <h2 class="h1 text-center">Societal Engagement Programs</h2>
            <li>Organize events where students participate in activities that benefit the local community, such as clean-up drives, food drives, or volunteering at local shelters.</li>
            <li>Encourages civic responsibility, builds empathy, and strengthens community ties.</li>
            <li>Collaborate with local businesses for sponsorships, internships, or mentorship programs. Businesses might provide resources, guest speakers, or opportunities for students to learn about various professions.</li>
            <li>To provide insights into the student's overall development, not just academic performance.</li>
            <li> Provides real-world learning experiences, enhances career readiness, and fosters community relationships.</li>
            <li>Host cultural festivals, art exhibitions, or performances that involve students and local artists. These events can showcase diverse cultures and talents within the community.</li>
            <li>Promotes cultural awareness, creativity, and community engagement.</li>
            <li>Implement programs focused on environmental sustainability, such as recycling drives, tree planting, or energy conservation projects.</li>
            <li>Educates students about environmental issues, fosters a sense of stewardship, and benefits the local environment.</li>
            <li>Organize workshops or seminars on topics of interest to both students and community members, such as health, technology, or financial literacy.</li>
            <li>Provides valuable learning opportunities for students and community members, strengthens school-community relationships.</li>
        </div>
    </div>

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <marquee><h3 class="text-white">This Website specifically for parents that effectively communicates important information and fosters engagement can be highly beneficial. </h3></marquee>
    </div>
    
    <div class="container-xxl py-3" style="background-color:var(--secondarycolor);">
        <div class="container m-3">
            <h2 class="h1 text-center">Never Skip Website</h2>
            <li>A personalized greeting from the school principal or head, emphasizing the role and importance of parents in the school community.</li>
            <li>Easy access to the most important sections like School Calendar, Parent Portal, Upcoming Events, and Contact Information.</li>
            <li>Secure login for parents to view their child’s grades, attendance, assignments, and communication with teachers.</li>
            <li>Include options for tracking academic progress, submitting forms, and scheduling parent-teacher conferences.</li>
            <li>A comprehensive calendar showcasing school holidays, parent-teacher meetings, extracurricular activities, and other important dates.</li>
            <li>Regular updates on school news, policy changes, and announcements.</li>
            <li>Details about the curriculum, learning objectives, and educational programs.</li>
            <li>Resources and guidelines to help parents support their children’s homework and study routines.</li>
            <li>Information on how parents can get involved, including volunteer roles and sign-up forms.</li>
        </div>
    </div>

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <a href="<?php echo e(route('contact-us')); ?>"><marquee><h3 class="text-white">If you’re looking to "Enquiry" section on your school’s website, <span class="text-danger">click here</span> </h3></marquee></a>
    </div>
    
    <div class="container-xxl py-3">
        <div class="container m-3">
            <h2 class="h1 text-center">Enquiry</h2>
            <li>It’s crucial to make it user-friendly and comprehensive to effectively handle questions and requests from parents, students, and other stakeholders.</li>
            <li>Provide phone numbers and email addresses</li>
            <li>Display a confirmation message once the form is submitted, indicating that the enquiry has been received and will be reviewed.</li>
            <li> Send an automated email to the user confirming receipt of their enquiry and providing an estimated response time.</li>
            <li>Implement a system to track and manage enquiries to ensure timely responses.</li>
            <li>Optionally, after an enquiry is resolved, ask users for feedback on their experience with the enquiry process.</li>
        </div>
    </div>

    <div class="container-xxl py-3" style="background-color:var(--thirdcolor);">
        <a href="<?php echo e(route('admissions')); ?>"><marquee><h3 class="text-white">If you’re looking to "Admission" section on your school’s website, <span class="text-danger">click here</span> </h3></marquee></a>
    </div>
    
    <div class="container-xxl py-3" style="background-color:var(--secondarycolor);">
        <div class="container m-3">
            <h2 class="h1 text-center">Admission</h2>
            <li>A friendly introduction to the admissions process, highlighting the school’s commitment to providing a great educational experience.</li>
            <li>Outline the steps required to apply, such as submitting an application form, attending an interview, or providing documentation.</li>
            <li> Provide a link to an online application form or portal where parents can submit their application.</li>
            <li>Provide information on how to schedule a campus tour or attend an open house.</li>
        </div>
    </div>

    <!-- Event -->
    <div class="container-xxl py-5">
        <div class="container">
            <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card flex-md-row mb-4 box-shadow h-md-250 bg-light box">
                <img class="card-img-right flex-auto d-md-none img-fluid" src="<?php echo e(asset('public/image/'.$event->image)); ?>" alt="Program Image" data-holder-rendered="true" style="width: 300px; height: 300px;">
                <div class="card-body d-flex flex-column align-items-center">
                    <h3 class="mb-0" style="display: block; text-align: center !important;">
                        <?php echo e($event->title); ?>

                    </h3><br>
                    <div class="mb-1">Date: <?php echo e($event->date); ?> <span>Time:  <?php echo e($event->time); ?></span></div>
                    
                    <div class="mb-1">Location: <?php echo e($event->location); ?></div>
                    <?php if(isset($event->short_desc)): ?><p><?php echo e($event->short_desc); ?></p><?php endif; ?>
                    <p class="card-text text-justify"><?php echo e($event->long_desc); ?></p>
                </div>
                <img class="card-img-right flex-auto d-none  d-md-block img-fluid" src="<?php echo e(asset('public/image/'.$event->image)); ?>" alt="Program Image" data-holder-rendered="true" style="width: 300px; height: 300px;">
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php echo e($events->links('pagination::bootstrap-5')); ?>

        </div>
    </div>
    <!--End Event -->
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layout.web', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\school_website\resources\views/web/events.blade.php ENDPATH**/ ?>