@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Enquiry /</span> Read List</h4>
    <ul class="nav nav-pills flex-column flex-md-row gap-2 gap-lg-0">
        {{-- count show using enquiryCount() --}}
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light" href="{{ route('enquiry.index') }}">All <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="all_count">0</span></a>
        </li>
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light active" href="{{ route('enquiry.read') }}">Read <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="read_count">0</span></a>
        </li>
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light" href="{{ route('enquiry.unread') }}">Unread <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="unread_count">0</span></a>
        </li>
    </ul> 
    <x-alert></x-alert>

    <div class="mt-3 mb-3">
        <form action="{{ route('enquiry.read') }}" method="GET">
            <div class="row text-dark mb-2">
                <div class="col-md-1">
                    <label for="" class="form-label">Name :</label>
                </div>
                <div class="col-md-3">
                    <input type="text" name="name" value="{{ $name }}" class="form-control border-2">
                </div>
                <div class="col-md-1"></div>
                <div class="col-md-1">
                    <label for="" class="form-label">Mobile :</label>
                </div>
                <div class="col-md-3">
                    <input type="text" name="mobile" value="{{ $mobile }}" class="form-control border-2">
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary" type="search">Search</button>
                </div>
            </div>
        </form>
    </div>
    
    <div class="card">
        <div class="table-responsive text-nowrap">
            <table class="table">
                <thead class="table-light">
                    <tr>
                        <th>Action</th>
                        <th>Status</th>
                        <th>SN.</th>
                        <th>Name</th>
                        <th>Mobile</th>
                        <th>Subject</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @forelse ($read_enquiries as $row)
                        <tr>
                            <td  class="text-center">
                                <form action="{{ route('enquiry.destroy',$row->id) }}" method="POST">
                                    @csrf
                                    @method('DELETE')
                                    <button class="dropdown-item waves-effect" type="submit" onclick="return confirm('Are You Sure to Delete?')"><i class="mdi mdi-trash-can-outline me-1 text-danger"></i></button>
                                </form>
                            </td>
                            <td class="text-center">
                                {{-- <input type="checkbox" {{ $row->status=="1" ? 'checked' : '' }} onclick="mark({{ $row->id }});"> --}}
                                <select id="status{{ $row->id }}" onchange="mark({{ $row->id }});" class="form-control">
                                    <option value="">Select</option>
                                    <option value="0" {{ $row->status=="0" ? 'selected' : '' }}>Unread</option>
                                    <option value="1" {{ $row->status=="1" ? 'selected' : '' }}>Read</option>
                                </select>
                            </td>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $row->name }}</td>
                            <td>{{ $row->mobile }}</td>
                            <td>{{ $row->subject }}</td>
                            <td>{{ $row->message }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td class="text-center" colspan="7">No Read Enquiry</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            {{ $read_enquiries->links('pagination::bootstrap-5') }}
        </div>
    </div>
@endsection
@push('script')
    <script>
        enquiryCount();
    </script>
@endpush