@forelse ($staffs as $row)
    <tr>
        <td class="text-center">
            <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                <i class="mdi mdi-dots-vertical"></i>
                </button>
                <div class="dropdown-menu">
                <a class="dropdown-item waves-effect" href="{{ route('staff.edit',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Edit</a>
                    <form action="{{ route('staff.destroy',$row->id) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button  class="dropdown-item waves-effect" type="submit" onclick="return confirm('Are You Sure to Delete?')"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</button>
                    </form>
                </div>
            </div>
        </td>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->name }}</td>
        <td>{{ $row->email }}</td>
        <td>{{ $row->status=="0" ? "Active" : "Inactive" }}</td>
    </tr>
@empty
    <tr>
        <td class="text-center" colspan="5">No Staff</td>
    </tr>
@endforelse