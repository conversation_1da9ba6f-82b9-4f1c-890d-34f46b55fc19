@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4">
        Update Password
    </h4>
    <div class="card">
        <div class="card-header">
            <h4>Update Password</h4>
        </div>
        <x-alert></x-alert>
        <form action="{{ route('user.password-change') }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            <div class="container">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Old Password <span class="text-danger"></span></label>
                        <input type="password" name="old_password" maxlength="12" minlength="8" class="form-control" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Password <span class="text-danger"></span></label>
                        <input type="password" name="password" maxlength="12" minlength="8" class="form-control" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Confirmed Password <span class="text-danger"></span></label>
                        <input type="password" name="password_confirmation" maxlength="12" minlength="8" class="form-control" required>
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </div>
            
        </form>
    </div>
@endsection