<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Banner;
use App\Models\Gallery;
use App\Models\News;
use App\Models\Event;
use App\Models\Syllabus;
use App\Models\Download;
use App\Models\Enquiry;
use App\Models\Admission;
use App\Models\User;

class AdminController extends Controller
{
    public function recycleBin(){
        $banners=Banner::select('id','image','deleted_at')->onlyTrashed()->get();
        $galleries=Gallery::select('id','image','deleted_at')->onlyTrashed()->get();
        $news=News::select('id','title','deleted_at')->onlyTrashed()->get();
        $events=Event::select('id','title','deleted_at')->onlyTrashed()->get();
        $syllabi=Syllabus::select('id','title','deleted_at')->onlyTrashed()->get();
        $downloads=Download::select('id','title','deleted_at')->onlyTrashed()->get();
        $enquiries=Enquiry::select('id','name','deleted_at')->onlyTrashed()->get();
        $admissions=Admission::select('id','name','deleted_at')->onlyTrashed()->get();
        $staffs=User::select('id','name','deleted_at')->onlyTrashed()->get();
        return view('app.recycle_bin.show',compact('banners','galleries','news','events','syllabi',
            'downloads','enquiries','admissions','staffs'));
    }
}
