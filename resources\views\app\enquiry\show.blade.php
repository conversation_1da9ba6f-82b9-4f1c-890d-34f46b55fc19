@extends('layout.app')
@section('content')
    <div>
      <h4 class="py-3 mb-4"><span class="text-muted fw-light">Enquiry /</span> All List</h4>
      <div>
          {{-- <a href="{{ route('enquiry.index') }}" class="btn btn-sm btn-primary me-2 col-md-1">All</a>
          <a href="{{ route('enquiry.unread') }}" class="btn btn-sm btn-primary me-2 col-md-1">Unread</a>
          <a href="{{ route('enquiry.read') }}" class="btn btn-sm btn-primary col-md-1">Read</a> --}}
          <ul class="nav nav-pills flex-column flex-md-row gap-2 gap-lg-0">
              {{-- count show using enquiryCount() --}}
              <li class="nav-item">
                  <a class="nav-link waves-effect waves-light active" href="{{ route('enquiry.index') }}">All <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-2" id="all_count">0</span></a>
              </li>
              <li class="nav-item">
                  <a class="nav-link waves-effect waves-light" href="{{ route('enquiry.read') }}">Read <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-2" id="read_count">0</span></a>
              </li>
              <li class="nav-item">
                  <a class="nav-link waves-effect waves-light" href="{{ route('enquiry.unread') }}">Unread <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-2" id="unread_count">0</span></a>
              </li>
          </ul> 
      </div>
      <x-alert></x-alert>

      <div class="mt-3 mb-3">
          <form action="{{ route('enquiry.index') }}" method="GET">
              <div class="row text-dark mb-2">
                  <div class="col-md-1">
                      <label for="" class="form-label">Name :</label>
                  </div>
                  <div class="col-md-3">
                      <input type="text" name="name" value="{{ $name }}" class="form-control border-2">
                  </div>
                  <div class="col-md-1"></div>
                  <div class="col-md-1">
                      <label for="" class="form-label">Mobile :</label>
                  </div>
                  <div class="col-md-3">
                      <input type="text" name="mobile" value="{{ $mobile }}" class="form-control border-2">
                  </div>
                  <div class="col-md-2">
                      <button class="btn btn-primary" type="search">Search</button>
                  </div>
              </div>
          </form>
      </div>
        
        <div class="card">
            <div class="table-responsive text-nowrap">
                <table class="table">
                    <thead class="table-light">
                        <tr>
                            <th>Action</th>
                            <th>Status</th>
                            <th>SN.</th>
                            <th>Name</th>
                            <th>Mobile</th>
                            <th>Subject</th>
                            <th>Message</th>
                        </tr>
                    </thead>
                    <tbody class="table-border-bottom-0">
                        @forelse ($enquiries as $row)
                            <tr>
                                <td  class="text-center">
                                    <form action="{{ route('enquiry.destroy',$row->id) }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <button class="dropdown-item waves-effect" type="submit" onclick="return confirm('Are You Sure to Delete?')"><i class="mdi mdi-trash-can-outline me-1 text-danger"></i></button>
                                    </form>
                                </td>
                                <td class="text-center">
                                    {{-- <input type="checkbox" {{ $row->status=="1" ? 'checked' : '' }} onclick="mark({{ $row->id }});"> --}}
                                    <select id="status{{ $row->id }}" onchange="mark({{ $row->id }});" class="form-control">
                                        <option value="">Select</option>
                                        <option value="0" {{ $row->status=="0" ? 'selected' : '' }}>Unread</option>
                                        <option value="1" {{ $row->status=="1" ? 'selected' : '' }}>Read</option>
                                    </select>
                                </td>
                                <td>{{ $loop->iteration }}</td>
                                <td>{{ $row->name }}</td>
                                <td>{{ $row->mobile }}</td>
                                <td>{{ $row->subject }}</td>
                                <td>{{ $row->message }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td class="text-center" colspan="7">No Enquiry</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
                {{ $enquiries->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    {{-- <div class="card">
        <h5 class="card-header">Light Table head</h5>
    <div class="table-responsive text-nowrap">
        <table class="table">
          <thead class="table-light">
            <tr>
              <th>Project</th>
              <th>Client</th>
              <th>Users</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody class="table-border-bottom-0">
            <tr>
              <td>
                <i class="mdi mdi-wallet-travel mdi-20px text-danger me-3"></i><span class="fw-medium">Tours Project</span>
              </td>
              <td>Albert Cook</td>
              <td>
                <ul class="list-unstyled users-list m-0 avatar-group d-flex align-items-center">
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Lilian Fuller" data-bs-original-title="Lilian Fuller">
                    <img src="../assets/img/avatars/5.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Sophia Wilkerson" data-bs-original-title="Sophia Wilkerson">
                    <img src="../assets/img/avatars/6.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Christina Parker" data-bs-original-title="Christina Parker">
                    <img src="../assets/img/avatars/7.png" alt="Avatar" class="rounded-circle">
                  </li>
                </ul>
              </td>
              <td><span class="badge rounded-pill bg-label-primary me-1">Active</span></td>
              <td>
                <div class="dropdown">
                  <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                    <i class="mdi mdi-dots-vertical"></i>
                  </button>
                  <div class="dropdown-menu">
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-pencil-outline me-1"></i> Edit</a>
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</a>
                  </div>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <i class="mdi mdi-basketball mdi-20px text-info me-3"></i><span class="fw-medium">Sports Project</span>
              </td>
              <td>Barry Hunter</td>
              <td>
                <ul class="list-unstyled users-list m-0 avatar-group d-flex align-items-center">
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Lilian Fuller" data-bs-original-title="Lilian Fuller">
                    <img src="../assets/img/avatars/5.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Sophia Wilkerson" data-bs-original-title="Sophia Wilkerson">
                    <img src="../assets/img/avatars/6.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Christina Parker" data-bs-original-title="Christina Parker">
                    <img src="../assets/img/avatars/7.png" alt="Avatar" class="rounded-circle">
                  </li>
                </ul>
              </td>
              <td><span class="badge rounded-pill bg-label-success me-1">Completed</span></td>
              <td>
                <div class="dropdown">
                  <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                    <i class="mdi mdi-dots-vertical"></i>
                  </button>
                  <div class="dropdown-menu">
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-pencil-outline me-1"></i> Edit</a>
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</a>
                  </div>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <i class="mdi mdi-greenhouse mdi-20px text-success me-3"></i><span class="fw-medium">Greenhouse Project</span>
              </td>
              <td>Trevor Baker</td>
              <td>
                <ul class="list-unstyled users-list m-0 avatar-group d-flex align-items-center">
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Lilian Fuller" data-bs-original-title="Lilian Fuller">
                    <img src="../assets/img/avatars/5.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Sophia Wilkerson" data-bs-original-title="Sophia Wilkerson">
                    <img src="../assets/img/avatars/6.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Christina Parker" data-bs-original-title="Christina Parker">
                    <img src="../assets/img/avatars/7.png" alt="Avatar" class="rounded-circle">
                  </li>
                </ul>
              </td>
              <td><span class="badge rounded-pill bg-label-info me-1">Scheduled</span></td>
              <td>
                <div class="dropdown">
                  <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                    <i class="mdi mdi-dots-vertical"></i>
                  </button>
                  <div class="dropdown-menu">
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-pencil-outline me-1"></i> Edit</a>
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</a>
                  </div>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <i class="mdi mdi-bank mdi-20px text-primary me-3"></i><span class="fw-medium">Bank Project</span>
              </td>
              <td>Jerry Milton</td>
              <td>
                <ul class="list-unstyled users-list m-0 avatar-group d-flex align-items-center">
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Lilian Fuller" data-bs-original-title="Lilian Fuller">
                    <img src="../assets/img/avatars/5.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Sophia Wilkerson" data-bs-original-title="Sophia Wilkerson">
                    <img src="../assets/img/avatars/6.png" alt="Avatar" class="rounded-circle">
                  </li>
                  <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar avatar-xs pull-up" aria-label="Christina Parker" data-bs-original-title="Christina Parker">
                    <img src="../assets/img/avatars/7.png" alt="Avatar" class="rounded-circle">
                  </li>
                </ul>
              </td>
              <td><span class="badge rounded-pill bg-label-warning me-1">Pending</span></td>
              <td>
                <div class="dropdown">
                  <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                    <i class="mdi mdi-dots-vertical"></i>
                  </button>
                  <div class="dropdown-menu">
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-pencil-outline me-1"></i> Edit</a>
                    <a class="dropdown-item waves-effect" href="javascript:void(0);"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</a>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
    </div>
    </div> --}}
@endsection
@push('script')
    <script>
        //Mark Read
        // function mark(id){
        //     if(confirm('Are You Sure to Mark?')){
        //         $.ajax({
        //             type: "GET",
        //             url: "{{ route('enquiry-update') }}",
        //             data: {
        //                 'id':id,
        //             },
        //             success: function (response) {
        //                 if(response==200){
        //                     alertify.set('notifier','position', 'top-right');
        //                     alertify.success('Mark Successfully!');
        //                 }else{
        //                     alertify.set('notifier','position', 'top-right');
        //                     alertify.error('Mark Not Successfully!');
        //                 }
        //             }
        //         });
        //     }
        // }

        // //All Table Show
        // $('#all').click(function (e) { 
        //     e.preventDefault();
        //     $('#all_table').show();
        //     $('#all_list').show();
        //     $('#read_table').hide();
        //     $('#unread_table').hide();
        //     $('#read_list').hide();
        //     $('#unread_list').hide();
        // });

        // //Read Table Show
        // $('#read').click(function (e) { 
        //     e.preventDefault();
        //     $('#all_table').hide();
        //     $('#all_list').hide();
        //     $('#read_table').show();
        //     $('#unread_table').hide();
        //     $('#read_list').show();
        //     $('#unread_list').hide();
        // });

        // //Unread Table Show
        // $('#unread').click(function (e) { 
        //     e.preventDefault();
        //     $('#all_table').hide();
        //     $('#all_list').hide();
        //     $('#read_table').hide();
        //     $('#unread_table').show();
        //     $('#read_list').hide();
        //     $('#unread_list').show();
        // });

        enquiryCount();
    </script>
@endpush