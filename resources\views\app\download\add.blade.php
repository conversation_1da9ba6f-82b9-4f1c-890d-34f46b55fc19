@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Download /</span> 
        @if (isset($download))
            Update
        @else
            Add
        @endif
    </h4>
    <div class="card">
        <div class="card-header">
            @if (isset($download))
                <h4>Update Download File</h4>
            @else
                <h4>Add Download File</h4>
            @endif
        </div>

        @if (isset($download))
            <form action="{{ route('download.update',$download->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
        @else
            <form action="{{ route('download.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
        @endif

                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Title <samp class="text-danger">*</samp></label>
                            <input type="text" name="title" value="@isset($download->title) {{ $download->title }} @endisset" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="0" @isset($download) {{ $download->status == "0" ? 'selected' : '' }} @endisset>Show</option>
                                <option value="1" @isset($download) {{ $download->status == "1" ? 'selected' : '' }} @endisset>Hide</option>
                            </select>
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Image <samp class="text-danger">*</samp>(Recommended image size 700 X 500 pixels)</label>
                            <input type="file" name="image"  class="form-control mb-3"  accept="image/*" @empty($download->image) {{ "required" }} @endempty>
                            @isset($download)
                                <img src="{{ asset('public/image/'.$download->image) }}" alt="Download Image" width="100px" height="100px"> 
                            @endisset
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Pdf</label>
                            <input type="file" name="pdf"  class="form-control mb-3" accept=".pdf">
                            @isset($download->pdf)
                                <embed src="{{ asset('public/pdf/'.$download->pdf) }}" type="" width="100px" height="100px" >
                            @endisset
                        </div>
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </form>
    </div>
@endsection