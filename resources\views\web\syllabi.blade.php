@extends('layout.web')

@section('content')
    <style>
        .embed-pdf {
            display: block; 
            width: 100%; 
            height: 400px; 
        }
        .pdf{
            display:none;
        }
        @media (max-width: 991px) {
            .embed-pdf,.title{
                display: none;
            }
            .pdf{
                display:block;
            }
        }
    </style>
    <x-codepen_card1></x-codepen_card1>

    <!-- Page Header End -->
    <div class="container-xxl py-5 page-header position-relative mb-5">
        <div class="container py-5">
            <h1 class="display-2 text-white animated slideInDown mb-4">Syllabus</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                    <li class="breadcrumb-item text-white active" aria-current="page">Syllabus</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header End -->

    <div class="container-xxl py-5 text-dark">
        <div class="container">
            <h2 class="text-center">Our Curriculum</h2>
            <p>
                When discussing a tech-driven curriculum, it’s about integrating technology into the educational framework to enhance learning experiences and outcomes. Here’s a breakdown of how technology can be woven into various aspects of the curriculum:</p>
    
                <p>
                    A tech-driven curriculum integrates technology to enhance learning by using tools like interactive math software, virtual labs, and adaptive learning platforms. It supports personalized education with digital assessments and e-portfolios, fosters collaboration through project-based learning, and encourages critical thinking with coding and simulation tools. It also includes ongoing professional development for educators and focuses on digital citizenship to ensure responsible technology use. This approach aims to make learning more engaging, efficient, and tailored to individual needs.</p>
        </div>
        
    </div>

    <!-- Gallary-->
    {{-- <div class="container-xxl py-3 ">
        <div class="container ">
            <div class="row g-5 align-items-center ">
                <div class="col-lg-12 col-md-12">
                    <h2 class="text-dark mb-4">Syllabus</h2>
                    <div class="row g-2 pt-2">
                        @foreach ($syllabi as $row)
                            <div class="col-lg-3 text-center">
                                <div class="card shadow">
                                    <a href="{{ route('syllabus',$row->slug) }}" style="margin-top:33px; margin-bottom:13px;">
                                        <img src="{{ asset('public/image/'.$row->image) }}" alt="Syllabus Image" width="250px" height="250px">
                                        <div class="text-center text-dark title">{{ $row->title }}</div>
                                    <a href="{{ route('syllabus',$row->slug) }}" class="btn btn-sm btn-primary  pdf col-12">{{ $row->title }}</a>
                                </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div> --}}
    <div class="album py-5 bg-body-tertiary">
        <div class="container">
            <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
                @foreach ($syllabi as $row)
                    <div class="col">
                        <div class="card shadow-sm gradient-card">
                            <div class="card-body">
                                <a href="{{ route('syllabus',$row->slug) }}">
                                    <h5 class="card-title">{{ $row->title }}</h5> 
                                </a>
                                    <p class="card-text">Brief description of the syllabus or key topics covered in this subject. This helps provide a quick overview of what the syllabus includes.</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="btn-group">
                                            <a href="{{ route('syllabus',$row->slug) }}" class="btn btn-sm btn-outline-light btn-pill">View</a>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <!-- End Gallary-->
@endsection