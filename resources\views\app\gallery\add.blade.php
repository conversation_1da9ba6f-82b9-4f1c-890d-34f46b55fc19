@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Gallery /</span> 
        @if (isset($gallery))
            Update
        @else
            Add
        @endif
    </h4>
    <div class="card">
        <div class="card-header">
            @if (isset($gallery))
                <h4>Update Gallery</h4>
            @else
                <h4>Add Gallery</h4>
            @endif
        </div>
        <x-alert></x-alert>
        
        @if (isset($gallery))
            <form action="{{ route('gallery.update',$gallery->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
        @else
            <form action="{{ route('gallery.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
        @endif

                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Image <samp class="text-danger">*</samp>(Recommended image size 700 X 500 pixels)</label>
                            <input type="file" name="image"  class="form-control mb-3" accept="image/*" @empty($gallery->image) required @endempty>
                            @isset($gallery)
                                <img src="{{ asset('public/image/'.$gallery->image) }}" alt="Gallery Image" width="100px" height="100px"> 
                            @endisset
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="0" @isset($gallery) {{ $gallery->status == "0" ? 'selected' : '' }} @endisset>Show</option>
                                <option value="1" @isset($gallery) {{ $gallery->status == "1" ? 'selected' : '' }} @endisset>Hide</option>
                            </select>
                        </div>
                        <div class="col-md-6 mt-3">
                            <label class="form-label">Sequence No.</label>
                            <input type="number" name="sequence_no" @isset($gallery)  value="{{ $gallery->sequence_no }}" @endisset maxlength="3" class="form-control">
                        </div>
                        <div class="mt-3 mb-3">
                            <button class="btn btn-danger me-2" type="reset">Reset</button>
                            <button class="btn btn-primary" type="submit">Submit</button>
                        </div>
                    </div>
                </div>
            </form>
    </div>
@endsection