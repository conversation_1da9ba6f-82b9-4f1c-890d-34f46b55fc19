@php
    use App\Models\Setting;
    $logo=Setting::where('key_name','logo')->pluck('value')->first();
    $favicon=Setting::where('key_name','favicon')->pluck('value')->first();
@endphp
<!DOCTYPE html>

<html
  lang="en"
  class="light-style layout-wide customizer-hide"
  dir="ltr">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title>BSPS | Login</title>

    <meta name="description" content="" />

    <!-- Favicon -->
    @isset($favicon)
        <link href="{{ asset('assets/'.$favicon) }}" rel="icon">
    @endisset

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap"
      rel="stylesheet" />

    <link rel="stylesheet" href="{{ asset('assets/app/vendor/fonts/materialdesignicons.css') }}" />

    <!-- Menu waves for no-customizer fix -->
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/libs/node-waves/node-waves.css') }}" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/css/core.css') }}" class="template-customizer-core-css" />
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/css/theme-default.css') }}" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="{{ asset('assets/app/css/demo.css') }}" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/libs/perfect-scrollbar/perfect-scrollbar.css') }}" />

    <!-- Page CSS -->
    <!-- Page -->
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/css/pages/page-auth.css') }}" />

    <!-- Helpers -->
    <script src="{{ asset('assets/app/vendor/js/helpers.js') }}"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script src="{{ asset('assets/app/js/config.js') }}"></script>

    <!-- Alertify js -->
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify_theme.css') }}">
    <script src="{{ asset('assets/alertify/alertify.js') }}"></script>
  </head>

  <body>
    <!-- Content -->

    <div class="position-relative">
      <div class="authentication-wrapper authentication-basic container-p-y">
        <div class="authentication-inner py-4">
          <!-- Login -->
          <div class="card p-2">
            <!-- Logo -->
            <div class="app-brand justify-content-center mt-5">
              @if (isset($logo))
                  <img src="{{ asset('assets/'.$logo) }}" alt="Logo Image" loading="lazy" height="50px;">
              @else
                  <img src="{{ asset('assets/logo1.png') }}" alt="Logo Image" loading="lazy" height="50px;">
              @endif
            </div>
            <!-- /Logo -->

            <div class="card-body mt-2">
              <p class="mb-4">Please sign-in to your account and start the adventure</p>

              <x-alert></x-alert>
              <form id="formAuthentication" class="mb-3" action="{{ route('login') }}" method="POST">
                @csrf
                <div class="form-floating form-floating-outline mb-3">
                  <input
                    type="text"
                    class="form-control"
                    id="email"
                    name="email"
                    placeholder="Enter your email"  value="{{ old('email') }}"
                    autofocus />
                  <label for="email">Email</label>
                </div>
                <div class="mb-3">
                  <div class="form-password-toggle">
                    <div class="input-group input-group-merge">
                      <div class="form-floating form-floating-outline">
                        <input
                          type="password"
                          id="password"
                          class="form-control"
                          name="password"
                          aria-describedby="password" maxlength="12" minlength="8" value="{{ old('password') }}" />
                        <label for="password">Password</label>
                      </div>
                      <span class="input-group-text cursor-pointer"><i class="mdi mdi-eye-off-outline"></i></span>
                    </div>
                  </div>
                </div>
                <div class="mb-3 d-flex justify-content-between">
                  <a href="{{ route('forgot-password') }}" class="float-end mb-1">
                    <span>Forgot Password?<span>
                  </a>
                </div>
                <div class="mb-3">
                  <button class="btn btn-primary d-grid w-100" type="submit">Sign in</button>
                </div>
              </form>
            </div>
          </div>
          <!-- /Login -->
          <img
            src="{{ asset('assets/app/img/illustrations/tree-3.png') }}"
            alt="auth-tree"
            class="authentication-image-object-left d-none d-lg-block" />
          <img
            src="{{ asset('assets/app/img/illustrations/auth-basic-mask-light.png') }}"
            class="authentication-image d-none d-lg-block"
            alt="triangle-bg"
            data-app-light-img="illustrations/auth-basic-mask-light.png"
            data-app-dark-img="illustrations/auth-basic-mask-dark.png" />
          <img
            src="{{ asset('assets/app/img/illustrations/tree.png') }}"
            alt="auth-tree"
            class="authentication-image-object-right d-none d-lg-block" />
        </div>
      </div>
    </div>

    <!-- / Content -->

    <!-- Core JS -->
    <!-- build:js assets/vendor/js/core.js -->
    <script src="{{ asset('assets/app/vendor/libs/jquery/jquery.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/libs/popper/popper.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/js/bootstrap.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/libs/node-waves/node-waves.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/libs/perfect-scrollbar/perfect-scrollbar.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/js/menu.js') }}"></script>

    <!-- endbuild -->

    <!-- Vendors JS -->

    <!-- Main JS -->
    <script src="{{ asset('assets/app/js/main.js') }}"></script>

    <!-- Page JS -->

    <!-- Place this tag in your head or just before your close body tag. -->
    <script async defer src="https://buttons.github.io/buttons.js"></script>
  </body>
</html>
