@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Settings /</span> Update Website Details</h4>
    <x-alert></x-alert>
    <div class="card">
        <div class="card-header">
            <h4>Update Website Detail</h4>
        </div>
            <form action="{{ route('website-detail-update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="container">
                    <div class="">
                        <label class="form-label">Name <span class="text-danger">*</span> </label>
                        <input type="text" name="website_name" @isset($website_name) value="{{ $website_name }}" @endisset class="form-control" >
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Url <span class="text-danger">*</span> </label>
                        <input type="url" name="website_url" @isset($website_url) value="{{ $website_url }}" @endisset class="form-control" >
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Iframe Soruce <span class="text-danger">*</span> </label>
                        <input type="text" name="location_iframe_src" @isset($location_iframe_src) value="{{ $location_iframe_src }}" @endisset class="form-control">
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2 waves-effect waves-light" type="reset">Reset</button>
                        <button class="btn btn-primary waves-effect waves-light" type="submit">Submit</button>
                    </div>
                </div>
            </form>
    </div>
@endsection