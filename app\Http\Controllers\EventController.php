<?php

namespace App\Http\Controllers;

use App\Models\Event;
use Illuminate\Http\Request;
use App\Trait\SaveFile;

class EventController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $events=Event::orderBy('id','desc')->paginate(10);
        return view('app.event.show',compact('events'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('app.event.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['title'=>'required|max:255|string',
            'date'=>'required|date',
            'time'=>'required|string',
            'location'=>'nullable|string|max:255',
            'short_desc'=>'nullable|string|max:255',
            'long_desc'=>'required|string',
            'image'=>'required|mimes:jpg,jpeg,png,webp,svg,gif']);
        $image=$this->saveImage($request['image']);
        try {
            Event::create(array_merge($request->except('_token','image'),['image'=>$image]));
            return to_route('event.index')->with('success','Event Aadded Successfully!');
        } catch (\Exception $ex) { 
            return back()->with('error','Event Is Not Aadded!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Event $event)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Event $event)
    {
        return view('app.event.add',compact('event'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Event $event)
    {
        $request->validate(['title'=>'required|max:255|string',
            'date'=>'required|date',
            'time'=>'required|string',
            'location'=>'nullable|string|max:255',
            'short_desc'=>'nullable|string|max:255',
            'long_desc'=>'required|string',
            'image'=>'mimes:jpg,jpeg,png,webp,svg,gif']);
        $image1=Event::where('id',$event->id)->pluck('image')->first();
        if(isset($request['image'])){
            $image=$this->saveImage($request['image']);
            if(file_exists(public_path('image/'.$image1))){
                unlink(public_path('image/'.$image1));
            }
        }else{
            $image=$image1;
        }
        try {
            Event::where('id',$event->id)->update(array_merge($request->except('_token','image','_method'),['image'=>$image]));
            return to_route('event.index')->with('success','Event Updated Successfully!');
        } catch (\Exception $ex) { 
            return back()->with('error','Event Is Not Updated!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Event $event)
    {
        try {
            $event->delete();
            return back()->with('success','Event Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Event Is Not Deleted!');
        }
    }

    public function restore($id){
        try {
            Event::where('id',$id)->restore();
            return back()->with('success','Event Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Event Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        $image=Event::withTrashed()->where('id',$id)->pluck('image')->first();
        try {
            Event::where('id',$id)->forceDelete();
            if(file_exists(public_path('image/'.$image))){
                unlink(public_path('image/'.$image));
            }
            return back()->with('success','Event Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Event Is Not Deleted!');
        }
    }
}
