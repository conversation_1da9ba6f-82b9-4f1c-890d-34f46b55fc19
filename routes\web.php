<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BannerController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\AdmissionController;
use App\Http\Controllers\EnquiryController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SyllabusController;
use App\Http\Controllers\DownloadController;
use App\Http\Controllers\SocialLinkController;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\SettingController;

//Website
Route::get('/',[WebController::class,'index'])->name('index');
Route::get('news-article',[WebController::class,'newsArticle'])->name('news-article');
Route::get('events',[WebController::class,'events'])->name('events');
Route::get('syllabuses',[WebController::class,'syllabuses'])->name('syllabuses');
Route::get('syllabuses/{slug}',[WebController::class,'syllabus'])->name('syllabus');
Route::get('admissions',[WebController::class,'admissions'])->name('admissions');
Route::post('admission-store',[AdmissionController::class,'store'])->name('admission.store');
Route::get('downloads',[WebController::class,'downloads'])->name('downloads');
Route::get('contact-us',[WebController::class,'contactUs'])->name('contact-us');
Route::post('enquiry-store',[EnquiryController::class,'store'])->name('enquiry.store');
Route::get('about-us',[WebController::class,'aboutUs'])->name('about-us');

//Admin
Route::view('login','app.login')->name('login');
Route::post('login',[AuthController::class,'login'])->name('login');
Route::get('forgot-password',[AuthController::class,'forgotPassword'])->name('forgot-password');
Route::get('generate-otp',[AuthController::class,'generateOtp'])->name('generate-otp');
Route::get('verify-otp-form/{email}',[AuthController::class,'verifyOtpForm'])->name('verify-otp-form');
Route::get('verify-otp',[AuthController::class,'verifyOtp'])->name('verify-otp');
Route::get('new-password',[AuthController::class,'newPassword'])->name('new-password');

Route::middleware(['auth'])->group(function () {
    Route::get('dashboard',[AuthController::class,'dashboard'])->name('dashboard');
    Route::get('logout',[AuthController::class,'logout'])->name('logout');

    //News
    Route::resource('news',NewsController::class);
    Route::get('news-restore/{id}',[NewsController::class,'restore'])->name('news-restore');
    Route::delete('news-pemanently-destroy/{id}',[NewsController::class,'permanentlyDestroy'])->name('news.permanently-destroy');

    //Event
    Route::resource('event',EventController::class);
    Route::get('event-restore/{id}',[EventController::class,'restore'])->name('event-restore');
    Route::delete('event-pemanently-destroy/{id}',[EventController::class,'permanentlyDestroy'])->name('event.permanently-destroy');

    //user
    Route::resource('user',UserController::class);
    Route::get('user-password',[UserController::class,'password'])->name('user.password');
    Route::put('user-password-change',[UserController::class,'passwordChange'])->name('user.password-change');
    Route::get('recycle-bin',[AdminController::class,'recycleBin'])->name('admin.recycle-bin');
});

//admin
Route::middleware(['auth','admin'])->group(function () {
    //Banner
    Route::resource('banner',BannerController::class);
    Route::get('banner-restore/{id}',[BannerController::class,'restore'])->name('banner-restore');
    Route::delete('banner-pemanently-destroy/{id}',[BannerController::class,'permanentlyDestroy'])->name('banner.permanently-destroy');

    //Gallery
    Route::resource('gallery',GalleryController::class);
    Route::get('gallery-restore/{id}',[GalleryController::class,'restore'])->name('gallery-restore');
    Route::delete('gallery-pemanently-destroy/{id}',[GalleryController::class,'permanentlyDestroy'])->name('gallery.permanently-destroy');

    //Admission
    Route::get('admission',[AdmissionController::class,'index'])->name('admission.index');
    Route::get('approved-admission',[AdmissionController::class,'approved'])->name('admission.approved');
    Route::get('declined-admission',[AdmissionController::class,'declined'])->name('admission.declined');
    Route::get('admission/{admission}',[AdmissionController::class,'show'])->name('admission.show');
    Route::delete('admission/{admission}',[AdmissionController::class,'destroy'])->name('admission.destroy');
    Route::get('admission-update',[AdmissionController::class,'admissionUpdate'])->name('admission-update');
    Route::get('admission-restore/{id}',[AdmissionController::class,'restore'])->name('admission-restore');
    Route::delete('admission-pemanently-destroy/{id}',[AdmissionController::class,'permanentlyDestroy'])->name('admission.permanently-destroy');
    Route::get('admission-count',[AdmissionController::class,'count'])->name('admission-count');

    //Setting
        //About
        Route::get('about',[SettingController::class,'about'])->name('about');
        Route::put('about-update',[SettingController::class,'aboutUpdate'])->name('about-update');

        //School Details
        Route::get('school-detail',[SettingController::class,'schoolDetail'])->name('school-detail');
        Route::put('school-detail-update',[SettingController::class,'schoolDetailUpdate'])->name('school-detail-update');

        //Social Media Link
        Route::get('social-link',[SettingController::class,'socialLink'])->name('social-link');
        Route::put('social-link-update',[SettingController::class,'socialLinkUpdate'])->name('social-link-update');

    //Syllabus
    Route::resource('syllabus',SyllabusController::class);
    Route::get('syllabus-restore/{id}',[SyllabusController::class,'restore'])->name('syllabus-restore');
    Route::delete('syllabus-pemanently-destroy/{id}',[SyllabusController::class,'permanentlyDestroy'])->name('syllabus.permanently-destroy');

    //Download
    Route::resource('download',DownloadController::class);
    Route::get('download-restore/{id}',[DownloadController::class,'restore'])->name('download-restore');
    Route::delete('download-pemanently-destroy/{id}',[DownloadController::class,'permanentlyDestroy'])->name('download.permanently-destroy');

    //Enquiry
    Route::get('enquiry',[EnquiryController::class,'index'])->name('enquiry.index');
    Route::get('read-enquiry',[EnquiryController::class,'read'])->name('enquiry.read');
    Route::get('unread-enquiry',[EnquiryController::class,'unread'])->name('enquiry.unread');
    Route::get('enquiry-update',[EnquiryController::class,'enquiryUpdate'])->name('enquiry-update');
    Route::delete('enquiry/{enquiry}',[EnquiryController::class,'destroy'])->name('enquiry.destroy');
    Route::get('enquiry-restore/{id}',[EnquiryController::class,'restore'])->name('enquiry-restore');
    Route::delete('enquiry-pemanently-destroy/{id}',[EnquiryController::class,'permanentlyDestroy'])->name('enquiry.permanently-destroy');
    Route::get('enquiry-count',[EnquiryController::class,'count'])->name('enquiry-count');

    //staff
    Route::resource('staff',StaffController::class);
    Route::get('staff-restore/{id}',[StaffController::class,'restore'])->name('staff-restore');
    Route::delete('staff-pemanently-destroy/{id}',[StaffController::class,'permanentlyDestroy'])->name('staff.permanently-destroy');
});

//Superadmin
Route::middleware(['auth', 'superadmin'])->group(function () {
    Route::get('website-detail',[SettingController::class,'websiteDetail'])->name('website-detail');
    Route::put('website-detail-update',[SettingController::class,'WebsiteDetailUpdate'])->name('website-detail-update');
});
