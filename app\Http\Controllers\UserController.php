<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Auth;
use Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user=User::where('id',Auth::user()->id)->first();
        return view('app.user.my-profile',compact('user'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name'=>'required|max:70|string',
            'email'=>[
                'string','max:70','email',
                Rule::unique('users')->ignore($user->id)
            ],
        ]);
        try {
            User::where('id',$user->id)->update(['name'=>$request['name'],
                'email'=>$request['email']]);
            return redirect()->back()->with('success','Details Updated Successfully!');
        } catch (\Exception $ex) {
            return redirect()->back()->with('error','Details Is Not Updated!');
        }

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function password()
    {
        return view('app.user.password');
    }

    public function passwordChange(Request $request)
    {
        $request->validate([
            'old_password'=>'required|max:12|min:8|string',
            'password'=>'required|confirmed|max:12|min:8',
        ]);
        try {
            if(password_verify($request['old_password'],Auth::user()->password)){
                User::where('email',Auth::user()->email)->update(['password'=>Hash::make($request['password'])]);
                return back()->with('success','Password Change Successfully!');
            }else{
                return back()->with('error','This Is Not Your Email Id!');
            }
            
        } catch (\Exception $ex) {
            return back()->with('error','Password Not Change');
        }
    }
}
