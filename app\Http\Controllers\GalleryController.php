<?php

namespace App\Http\Controllers;

use App\Models\Gallery;
use Illuminate\Http\Request;
use App\Trait\SaveFile;

class GalleryController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $galleries=Gallery::select('id','image','status','sequence_no')->orderBy('id','desc')->paginate(10);
        return view('app.gallery.show',compact('galleries'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('app.gallery.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['image'=>'required|mimes:jpg,jpeg,png,webp,svg,gif',
            'status'=>'required']);
        $image=$this->saveImage($request['image']);
        try {
            Gallery::create(array_merge($request->except('_token','image'),['image'=>$image]));
            return to_route('gallery.index')->with('success','Gallery Aadded Successfully!');
        } catch (\Exception $ex) { 
            return back()->with('error','Gallery Is Not Aadded!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Gallery $gallery)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Gallery $gallery)
    {
        return view('app.gallery.add',compact('gallery'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Gallery $gallery)
    {
        $request->validate(['image'=>'mimes:jpg,jpeg,png,webp,svg,gif',
            'status'=>'required']);
        $image1=Gallery::where('id',$gallery->id)->pluck('image')->first();
        if(isset($request['image'])){
            $image=$this->saveImage($request['image']);
            if(file_exists(public_path('image/'.$image1))){
                unlink(public_path('image/'.$image1));
            }
        }else{
            $image=$image1;
        }
        try {
            Gallery::where('id',$gallery->id)->update(array_merge($request->except('_token','image','_method'),['image'=>$image]));
            return to_route('gallery.index')->with('success','Gallery Updated Successfully!');
        } catch (\Exception $ex) { 
            return back()->with('error','Gallery Is Not Updated!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Gallery $gallery)
    {
        try {
            $gallery->delete();
            return back()->with('success','Gallery Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Gallery Is Not Deleted!');
        }
    }

    
    public function restore($id){
        try {
            Gallery::where('id',$id)->restore();
            return back()->with('success','Gallery Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Gallery Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        $image=Gallery::withTrashed()->where('id',$id)->pluck('image')->first();
        try {
            Gallery::where('id',$id)->forceDelete();
            if(file_exists(public_path('image/'.$image))){
                unlink(public_path('image/'.$image));
            }
            return back()->with('success','Gallery Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Gallery Is Not Deleted!');
        }
    }
}
