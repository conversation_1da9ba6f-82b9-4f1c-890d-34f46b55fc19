@php
    use App\Models\Setting;
    $location_iframe_src=Setting::where('key_name','location_iframe_src')->pluck('value')->first();
@endphp
@extends('layout.web')

@section('content')
    <!-- Page Header End -->
    <div class="container-xxl py-5 page-header position-relative mb-5">
        <div class="container py-5">
            <h1 class="display-2 text-white animated slideInDown mb-4">Contact Us</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                    <li class="breadcrumb-item text-white active" aria-current="page">Contact Us</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Contact Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
                <h3 class="text-primary">Any Questions? We Have Got All the Answers!</h3>
                <h1 class="mb-3">Get In Touch</h1>
                <p>We're here to help! If you have any questions, comments, or need assistance, please don't hesitate to reach out. You can contact us via email , give us a call , or fill out the contact form below. We strive to respond to all inquiries within 24 hours. </p>
            </div>
            <div class="row g-4 mb-5">
                <div class="col-md-6 col-lg-3 text-center wow fadeInUp" data-wow-delay="0.1s">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 75px; height: 75px;">
                        <i class="fas fa-school fa-2x text-primary"></i>
                    </div>
                    <h6>@isset($school_name) {{ $school_name }} @endisset</h6>
                </div>
                <div class="col-md-6 col-lg-3 text-center wow fadeInUp" data-wow-delay="0.1s">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 75px; height: 75px;">
                        <i class="fa fa-map-marker-alt fa-2x text-primary"></i>
                    </div>
                    <h6>@isset($address) {{ $address }} @endisset</h6>
                </div>
                <div class="col-md-6 col-lg-3 text-center wow fadeInUp" data-wow-delay="0.3s">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 75px; height: 75px;">
                        <i class="fa fa-envelope-open fa-2x text-primary"></i>
                    </div>
                    <h6>@isset($email) {{ $email }} @endisset</h6>
                </div>
                <div class="col-md-6 col-lg-3 text-center wow fadeInUp" data-wow-delay="0.5s">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 75px; height: 75px;">
                        <i class="fa fa-phone-alt fa-2x text-primary"></i>
                    </div>
                    <h6> @isset($mobile) +91 {{ $mobile }} @endisset</h6>
                </div>
            </div>
            <div class="bg-light rounded">
                <div class="row g-0">
                    <div class="col-lg-6 wow fadeIn" data-wow-delay="0.1s">
                        <div class="h-100 d-flex flex-column justify-content-center p-5">
                            <h4 class="mb-3">Book Your Enquiry</h4>
                            <x-alert></x-alert>
                            <form action="{{ route('enquiry.store') }}" method="POST">
                                @csrf
                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="name" name="name" value="{{ old('name') }}" placeholder="Your Name" required>
                                            <label for="name">Your Name *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="mobile" class="form-control border-2" id="mobile" name="mobile" value="{{ old('mobile') }}" placeholder="Your Mobile" required>
                                            <label for="mobile">Mobile *</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-floating">
                                            <input type="email" class="form-control border-2" id="email" name="email" value="{{ old('email') }}" placeholder="Email (If Fill Notification Send This Email)">
                                            <label for="email">Email (If Fill Notification Send This Email)</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="subject" name="subject" value="{{ old('subject') }}" placeholder="Subject" required>
                                            <label for="subject">Subject *</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-floating">
                                            <textarea class="form-control border-2" placeholder="Leave a message here" id="message" name="message"  style="height: 100px" required>{{ old('message') }}</textarea>
                                            <label for="message">Message *</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <button class="btn btn-primary w-100 py-3" type="submit">Send Message</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-lg-6 wow fadeIn" data-wow-delay="0.5s" style="min-height: 400px;">
                        <div class="position-relative h-100">
                            <iframe class="position-relative rounded w-100 h-100"
                            src="@isset($location_iframe_src){{ $location_iframe_src }} @endisset"
                            frameborder="0" style="min-height: 400px; border:0;" allowfullscreen="" aria-hidden="false"
                            tabindex="0"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Contact End -->
@endsection