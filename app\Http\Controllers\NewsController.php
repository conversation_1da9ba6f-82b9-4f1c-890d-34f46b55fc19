<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;
use App\Trait\SaveFile;

class NewsController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $news=News::orderBy('id','desc')->paginate(10);
        return view('app.news.show',compact('news'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('app.news.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['title'=>'required|max:255|string',
            'date'=>'required|date',
            'time'=>'required|string',
            'location'=>'nullable|string|max:255',
            'short_desc'=>'nullable|string|max:255',
            'long_desc'=>'required|string',
            'image'=>'required|mimes:jpg,jpeg,png,webp,svg,gif']);
        $image=$this->saveImage($request['image']);
        try {
            News::create(array_merge($request->except('_token','image'),['image'=>$image]));
            return to_route('news.index')->with('success','News Aadded Successfully!');
        } catch (\Exception $ex) { 
            return back()->with('error','News Is Not Aadded!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(News $news)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(News $news)
    {
        return view('app.news.add',compact('news'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, News $news)
    {
        $request->validate(['title'=>'required|max:255|string',
            'date'=>'required|date',
            'time'=>'required|string',
            'location'=>'nullable|string|max:255',
            'short_desc'=>'nullable|string|max:255',
            'long_desc'=>'required|string',
            'image'=>'mimes:jpg,jpeg,png,webp,svg,gif']);
        $image1=News::where('id',$news->id)->pluck('image')->first();
        if(isset($request['image'])){
            $image=$this->saveImage($request['image']);
            if(file_exists(public_path('image/'.$image1))){
                unlink(public_path('image/'.$image1));
            }
        }else{
            $image=$image1;
        }
        try {
            News::where('id',$news->id)->update(array_merge($request->except('_token','image','_method'),['image'=>$image]));
            return to_route('news.index')->with('success','News Updated Successfully!');
        } catch (\Exception $ex) { 
            return back()->with('error','News Is Not Updated!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(News $news)
    {
        try {
            $news->delete();
            return back()->with('success','News Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','News Is Not Deleted!');
        }
    }

    public function restore($id){
        try {
            News::where('id',$id)->restore();
            return back()->with('success','News Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','News Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        $image=News::withTrashed()->where('id',$id)->pluck('image')->first();
        try {
            News::where('id',$id)->forceDelete();
            if(file_exists(public_path('image/'.$image))){
                unlink(public_path('image/'.$image));
            }
            return back()->with('success','News Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','News Is Not Deleted!');
        }
    }
}
