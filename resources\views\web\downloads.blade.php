@extends('layout.web')

@push('style')
    <x-codepen_card></x-codepen_card>
@endpush

@section('content')
    <!-- Page Header End -->
    <div class="container-xxl py-5 page-header position-relative mb-5">
        <div class="container py-5">
            <h1 class="display-2 text-white animated slideInDown mb-4">Downloads</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                    <li class="breadcrumb-item text-white active" aria-current="page">Downloads</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Download -->
    {{-- <div class="container-xxl py-3 ">
        <div class="container ">
            <div class="row g-5 align-items-center ">
                <div class="col-lg-12 col-md-12">
                    <h2 class="text-dark mb-4">Downloads </h2>
                    <div class="row g-2 pt-2">
                        @foreach ($downloads as $row)
                            <div class="col-lg-3">
                                <div class="  card shadow">
                                    <div class="text-center" style="margin-top:33px; margin-bottom:13px;">
                                        <img src="{{ asset('public/image/'.$row->image) }}" alt="Download Image" width="250px" height="250px">
                                        <div class="text-center text-dark title">{{ $row->title }}</div>
                                    </div>
                                </div>
                                <div class="text-center">
                                    @if(empty($row->pdf))
                                        <a class="btn btn-primary mt-2 href="{{ asset('public/image/'.$row->image) }}" download>Download Image</a>
                                    @else
                                        <a class="btn btn-primary mt-2" href="{{ asset('public/image/'.$row->image) }}" download>Download Image</a>
                                        <a class="btn btn-primary mt-2" href="{{ asset('public/pdf/'.$row->pdf) }}" target="_blank">Show Pdf</a>
                                        <a class="btn btn-primary mt-2" href="{{ asset('public/pdf/'.$row->pdf) }}" download>Download Pdf</a>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div> --}}

    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>Downloads</h1><hr>
            </div>
        </div>
            
        @foreach ($downloads as $row)
            <div class="row m-t-80">
                <div class="col-md-4 col-xs-12">
                    <div class="card">
                        <img class="card-img-top" src="{{ asset('public/image/'.$row->image) }}" alt="Download Image">
                        <div class="card-body text-center">
                            <h4 class="card-title">{{ $row->title }}</h4>
                            @if(empty($row->pdf))
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="{{ asset('public/image/'.$row->image) }}" download><i class="fa-regular fa-circle-down"></i> Image</a>
                            @else
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="{{ asset('public/image/'.$row->image) }}" download><i class="fa-regular fa-circle-down"></i> Image</a>
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="{{ asset('public/pdf/'.$row->pdf) }}" target="_blank"><i class="fa-regular fa-eye"></i> Pdf</a>
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="{{ asset('public/pdf/'.$row->pdf) }}" download><i class="fa-regular fa-circle-down"></i> Pdf</a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

@endsection