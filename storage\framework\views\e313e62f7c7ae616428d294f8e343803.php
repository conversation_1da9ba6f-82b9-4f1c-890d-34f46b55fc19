<?php $__env->startPush('style'); ?>
    <?php if (isset($component)) { $__componentOriginal4e1d8f8bdc494eda925148f6b676eb81 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4e1d8f8bdc494eda925148f6b676eb81 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.codepen_card','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('codepen_card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4e1d8f8bdc494eda925148f6b676eb81)): ?>
<?php $attributes = $__attributesOriginal4e1d8f8bdc494eda925148f6b676eb81; ?>
<?php unset($__attributesOriginal4e1d8f8bdc494eda925148f6b676eb81); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4e1d8f8bdc494eda925148f6b676eb81)): ?>
<?php $component = $__componentOriginal4e1d8f8bdc494eda925148f6b676eb81; ?>
<?php unset($__componentOriginal4e1d8f8bdc494eda925148f6b676eb81); ?>
<?php endif; ?>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header End -->
    <div class="container-xxl py-5 page-header position-relative mb-5">
        <div class="container py-5">
            <h1 class="display-2 text-white animated slideInDown mb-4">Downloads</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('index')); ?>">Home</a></li>
                    <li class="breadcrumb-item text-white active" aria-current="page">Downloads</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Download -->
    

    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>Downloads</h1><hr>
            </div>
        </div>
            
        <?php $__currentLoopData = $downloads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="row m-t-80">
                <div class="col-md-4 col-xs-12">
                    <div class="card">
                        <img class="card-img-top" src="<?php echo e(asset('public/image/'.$row->image)); ?>" alt="Download Image">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo e($row->title); ?></h4>
                            <?php if(empty($row->pdf)): ?>
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="<?php echo e(asset('public/image/'.$row->image)); ?>" download><i class="fa-regular fa-circle-down"></i> Image</a>
                            <?php else: ?>
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="<?php echo e(asset('public/image/'.$row->image)); ?>" download><i class="fa-regular fa-circle-down"></i> Image</a>
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="<?php echo e(asset('public/pdf/'.$row->pdf)); ?>" target="_blank"><i class="fa-regular fa-eye"></i> Pdf</a>
                                <a class="btn btn-outline-success btn-lg btn-block btnalt" href="<?php echo e(asset('public/pdf/'.$row->pdf)); ?>" download><i class="fa-regular fa-circle-down"></i> Pdf</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layout.web', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\school_website\resources\views/web/downloads.blade.php ENDPATH**/ ?>