@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Staff /</span> 
        @if (isset($staff))
            Update
        @else
            Add
        @endif
    </h4>
    <div class="card">
        <div class="card-header">
            @if (isset($staff))
                <h4>Update Staff</h4>
            @else
                <h4>Add Staff</h4>
            @endif
        </div>
        <x-alert></x-alert>

        @if (isset($staff))
            <form action="{{ route('staff.update',$staff->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
        @else
            <form action="{{ route('staff.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
        @endif

                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Name <samp class="text-danger">*</samp></label>
                            <input type="text" name="name" value="@isset($staff) {{ $staff->name }} @endisset" class="form-control">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Email <samp class="text-danger">*</samp></label>
                            <input type="email" name="email" @isset($staff) value="{{ $staff->email }}" @endisset class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Password <samp class="text-danger">*</samp></label>
                            <input type="password" name="password" class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Confirmed Password <samp class="text-danger">*</samp></label>
                            <input type="password" name="password_confirmation" class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                {{-- <option value="0" @isset($staff) {{ $staff->status == "0" ? 'selected' : '' }} @endisset>Active</option>
                                <option value="1" @isset($staff) {{ $staff->status == "1" ? 'selected' : '' }} @endisset>Inactive</option> --}}
                                <option value="">Select</option>
                                <option value="0" @selected(old('status',$staff->status ?? '') == "0") >Active</option>
                                <option value="1" @selected(old('status',$staff->status ?? '') == "1") >In Active</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </form>
    </div>
@endsection