<?php

namespace App\Http\Controllers;

use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Trait\SaveFile;

class BannerController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $banners=Banner::paginate(10);
        return view('app.banner.show',compact('banners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('app.banner.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['title'=>'nullable|max:70|string',
            'description'=>'nullable|string',
            'image'=>'required|mimes:jpg,jpeg,webp,png,gif,svg',
            'sequence_no'=>'required|max:3']);

        try {
            $image=$this->saveImage($request['image']);

            Banner::create(array_merge($request->except('_token','image'),['image'=>$image]));
            return redirect()->route('banner.index')->with('success','Banner Added Successfully!');
        } catch (\Exception $ex) {
            return redirect()->back()->with('error','Banner Is Not Added!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Banner $banner)
    {
        
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Banner $banner)
    {
        $edit=Banner::where('id',$banner->id)->get();
        return view('app.banner.add',compact('edit'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Banner $banner)
    {
        $request->validate(['title'=>'nullable|max:70|string',
            'description'=>'nullable|string',
            'image'=>'mimes:jpg,jpeg,webp,png,gif,svg',
            'sequence_no'=>'required|max:3']);
        $image1=Banner::where('id',$banner->id)->pluck('image')->first();
        if(!empty($request['image'])){
            
            if(file_exists(public_path('image/'.$image1))){
                unlink(public_path('image/'.$image1));
            }
            $image = $this->saveImage($request['image']);
        }else{
            $image=$image1;
        }

        try {
            Banner::where('id',$banner->id)->update(array_merge($request->except('_token','image','_method'),['image'=>$image]));
            return redirect()->route('banner.index')->with('success','Banner Updated Successfully!');
        } catch (\Exception $ex) {
            return redirect()->back()->with('error','Banner Is Not Updated!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Banner $banner)
    {
        try {
            Banner::where('id',$banner->id)->delete();
            return redirect()->back()->with('success','Banner Deleted Suucessfully');
        } catch (\Exception $ex) {
            return redirect()->back()->with('error','Banner Is Not Deleted');
        }
    }

    public function restore($id){
        try {
            Banner::where('id',$id)->restore();
            return back()->with('success','Banner Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Banner Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        $image=Banner::withTrashed()->where('id',$id)->pluck('image')->first();
        try {
            Banner::where('id',$id)->forceDelete();
            if(file_exists(public_path('image/'.$image))){
                unlink(public_path('image/'.$image));
            }
            return back()->with('success','Banner Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Banner Is Not Deleted!');
        }
    }
}
