<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Setting $setting)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Setting $setting)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Setting $setting)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Setting $setting)
    {
        //
    }

    public function about()
    {
        $about=Setting::where('key_name','about')->pluck('value')->first();
        return view('app.about.add',compact('about'));
    }

    public function aboutUpdate(Request $request)
    {
        $request->validate(['about'=>'string|required']);
        try {
            Setting::where('key_name','about')->update(['value'=>$request['about']]);
            return back()->with('success','About Us Updated Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','About Us Is Not Updated!');
        }
    }

    public function schoolDetail()
    {
        $school_name=Setting::where('key_name','school_name')->pluck('value')->first();
        $email=Setting::where('key_name','email')->pluck('value')->first();
        $address=Setting::where('key_name','address')->pluck('value')->first();
        $mobile=Setting::where('key_name','mobile')->pluck('value')->first();
        return view('app.school_detail.add',compact('school_name','email','address','mobile'));
    }

    public function schoolDetailUpdate(Request $request)
    {
        $request->validate(['school_name'=>'string|required|max:255',
            'address'=>'string|required',
            'mobile'=>'required|max:10|min:10',
            'email'=>'email|required|max:70',]);                                                      
        try {
            Setting::where('key_name','school_name')->update(['value'=>$request['school_name']]);
            Setting::where('key_name','address')->update(['value'=>$request['address']]);
            Setting::where('key_name','mobile')->update(['value'=>$request['mobile']]);
            Setting::where('key_name','email')->update(['value'=>$request['email']]);
            return back()->with('success','School Detail Us Updated Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','School Detail Is Not Updated!');
        }
    }

    public function socialLink()
    {
        $facebook=Setting::where('key_name','facebook')->pluck('value')->first();
        $linkedin=Setting::where('key_name','linkedin')->pluck('value')->first();
        $twitter=Setting::where('key_name','twitter')->pluck('value')->first();
        $youtube=Setting::where('key_name','youtube')->pluck('value')->first();
        return view('app.social_link.add',compact('facebook','linkedin','twitter','youtube'));
    }

    public function socialLinkUpdate(Request $request){
        $request->validate(['facebook'=>'url|nullable',
            'twitter'=>'url|nullable',
            'youtube'=>'nullable|url',
            'linkedin'=>'nullable|url']);                                                      
        try {
            Setting::where('key_name','facebook')->update(['value'=>empty($request['facebook'])  ? "#" : $request['facebook']  ]);
            Setting::where('key_name','twitter')->update(['value'=>empty($request['twitter']) ? "#" :  $request['twitter']  ]);
            Setting::where('key_name','youtube')->update(['value'=>empty($request['youtube']) ? "#" :  $request['youtube']  ]);
            Setting::where('key_name','linkedin')->update(['value'=>empty($request['linkedin'])  ? "#" : $request['linkedin']  ]);
            return back()->with('success','Social Link Updated Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Social Link Is Not Updated!');
        }
    }

    public function websiteDetail()
    {
        $website_name=Setting::where('key_name','website_name')->pluck('value')->first();
        $website_url=Setting::where('key_name','website_url')->pluck('value')->first();
        $location_iframe_src=Setting::where('key_name','location_iframe_src')->pluck('value')->first();
        return view('app.website_detail.add',compact('website_name','website_url','location_iframe_src'));
    }

    public function WebsiteDetailUpdate(Request $request){
        $request->validate(['website_name'=>'string|required',
            'location_iframe_src'=>'string|required',
            'website_url'=>'nullable|url|required']);                                                      
        try {
            Setting::where('key_name','website_name')->update(['value'=>empty($request['website_name'])  ? "#" : $request['website_name']  ]);
            Setting::where('key_name','location_iframe_src')->update(['value'=>empty($request['location_iframe_src']) ? "#" :  $request['location_iframe_src']  ]);
            Setting::where('key_name','website_url')->update(['value'=>empty($request['website_url'])  ? "#" : $request['website_url']  ]);
            return back()->with('success','Website Details Updated Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Website Details Is Not Updated!');
        }
    }
}
