@extends('layout.web')

@section('content')
    <!-- Page Header End -->
    <div class="container-xxl py-5 page-header position-relative mb-5">
        <div class="container py-5">
            <h1 class="display-2 text-white animated slideInDown mb-4">Addmission</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('index') }}">Home</a></li>
                    <li class="breadcrumb-item text-white active" aria-current="page">Addmission</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->


    <!-- Addmission Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="text-center mb-4">
                <div class="text-primary">
                    <h5>1. Fill the School Admission Form on the Website</h5>
                    <h5>2. We will invite you to visit oue school for Campous Tour</h5>
                    <h5>3. Join the our Family</h5>
                </div>
            </div>
            <div class="bg-light rounded">
                <div class="row g-0">
                    <div class="col-lg-12 wow fadeIn" data-wow-delay="0.1s">
                        <div class="h-100 d-flex flex-column justify-content-center p-5">
                            <h1 class="mb-4">Make Admission</h1>
                            <x-alert></x-alert>
                            <form action="{{ route('admission.store') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <select name="class_program"  id="class_program" class="form-control border-2"  placeholder="Class Program *" required>
                                                <option value="Nursery" {{ old('class_program')=="Nursery" ? 'selected' : ''  }}>Nursery</option>
                                                <option value="KG1" {{ old('class_program')=="KG1" ? 'selected' : ''  }}>KG1</option>
                                                <option value="KG2" {{ old('class_program')=="KG2" ? 'selected' : ''  }}>KG2</option>
                                                <option value="1st" {{ old('class_program')=="1st" ? 'selected' : ''  }}>1st</option>
                                                <option value="2nd" {{ old('class_program')=="2nd" ? 'selected' : ''  }}>2nd</option>
                                                <option value="3rd" {{ old('class_program')=="3rd" ? 'selected' : ''  }}>3rd</option>
                                                <option value="4th" {{ old('class_program')=="4th" ? 'selected' : ''  }}>4th</option>
                                                <option value="5th" {{ old('class_program')=="5th" ? 'selected' : ''  }}>5th</option>
                                                <option value="6th" {{ old('class_program')=="6th" ? 'selected' : ''  }}>6th</option>
                                                <option value="7th" {{ old('class_program')=="7th" ? 'selected' : ''  }}>7th</option>
                                                <option value="8th" {{ old('class_program')=="8th" ? 'selected' : ''  }}>8th</option>
                                                <option value="9th" {{ old('class_program')=="9th" ? 'selected' : ''  }}>9th</option>
                                                <option value="10th" {{ old('class_program')=="10th" ? 'selected' : ''  }}>10th</option>
                                                <option value="11th" {{ old('class_program')=="11th" ? 'selected' : ''  }}>11th</option>
                                                <option value="12th" {{ old('class_program')=="12th" ? 'selected' : ''  }}>12th</option>
                                                
                                            </select>
                                            <label for="class_program">Class Program *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="name" name="name" value="{{ old('name') }}" placeholder="Name *" required>
                                            <label for="name">Name *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="father_name" name="father_name" value="{{ old('father_name') }}" placeholder="Father Name *" required>
                                            <label for="father_name">Father Name *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="mother_name" name="mother_name" value="{{ old('mother_name') }}" placeholder="Mother Name *" required>
                                            <label for="mother_name">Mother Name *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="date" class="form-control border-2" id="dob" name="dob" value="{{ old('dob') }}" placeholder="DOB *" required>
                                            <label for="dob">DOB *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating border-2 form-control">
                                            <div class="">Gender *</div>
                                            <div class="row">
                                                <div class="col-6">
                                                    <input type="radio" id="gender" name="gender"  placeholder="Male" value="male" {{ old('gender')=="male" ? 'checked' : ''  }}> Male
                                                </div>
                                                <div class="col-6">
                                                    <input type="radio" id="gender" name="gender"  placeholder="Femaile" value="female" {{ old('gender')=="female" ? 'checked' : ''  }}> Female
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <select name="blood_group"  id="blood_group" class="form-control border-2"  placeholder="Blood Group *" required>
                                                <option value="" {{ old('blood_group')=="" ? 'selected' : ''  }}>O-</option>
                                                <option value="O+" {{ old('blood_group')=="O+" ? 'selected' : ''  }}>O+</option>
                                                <option value="A-" {{ old('blood_group')=="A-" ? 'selected' : ''  }}>A-</option>
                                                <option value="A+" {{ old('blood_group')=="A+" ? 'selected' : ''  }}>A+</option>
                                                <option value="B-" {{ old('blood_group')=="B-" ? 'selected' : ''  }}>B-</option>
                                                <option value="B+" {{ old('blood_group')=="B+" ? 'selected' : ''  }}>B+</option>
                                                <option value="AB-" {{ old('blood_group')=="AB-" ? 'selected' : ''  }}>AB-</option>
                                                <option value="AB+" {{ old('blood_group')=="AB+" ? 'selected' : ''  }}>AB+</option>
                                            </select>
                                            <label for="blood_group">Blood Group </label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <select name="parent_occupation"  id="parent_occupation" class="form-control border-2"  placeholder="Parent Occupation *" required>
                                                <option value="Bussiness" {{ old('parent_occupation')=="Bussiness" ? 'selected' : ''  }}>Bussiness</option>
                                                <option value="Self Employeed" {{ old('parent_occupation')=="Self Employeed" ? 'selected' : ''  }}>Self Employeed</option>
                                                <option value="Employee" {{ old('parent_occupation')=="Employee" ? 'selected' : ''  }}>Employee</option>
                                                <option value="Other" {{ old('parent_occupation')=="Other" ? 'selected' : ''  }}>Other</option>
                                            </select>
                                            <label for="parent_occupation">Parent Occupation *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <textarea name="address" class="form-control border-2"  id="address"  placeholder="Address *" required cols="30" rows="10">{{ old('address') }}</textarea>
                                            <label for="address">Address *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <select name="caste"  id="caste" class="form-control border-2"  placeholder="Caste *" required>
                                                <option value="General" {{ old('caste')=="General" ? 'selected' : ''  }}>General</option>
                                                <option value="OBC" {{ old('caste')=="OBC" ? 'selected' : ''  }}>OBC</option>
                                                <option value="SC" {{ old('caste')=="SC" ? 'selected' : ''  }}>SC</option>
                                                <option value="ST" {{ old('caste')=="ST" ? 'selected' : ''  }}>ST</option>
                                            </select>
                                            <label for="caste">Caste *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control border-2" id="mobile1" name="mobile1" value="{{ old('mobile1') }}" placeholder="Mobile 1 *" required>
                                            <label for="mobile1">Mobile 1 *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control border-2" id="mobile2" name="mobile2" value="{{ old('mobile2') }}" placeholder="Mobile 2">
                                            <label for="mobile2">Mobile 2</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="email" class="form-control border-2" id="email" name="email" value="{{ old('email') }}" placeholder="Email (If Fill Notification Send This Email)">
                                            <label for="email">Email (If Fill Notification Send This Email)</label>
                                        </div>
                                    </div>
                                    <h5>Details of Id proof : Candidate</h5>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="birth_certificate" name="birth_certificate" value="{{ old('birth_certificate') }}" placeholder="Birth Certificate *" required>
                                            <label for="birth_certificate">Birth Certificate *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="number" class="form-control border-2" id="aadhar_card_candidate" name="aadhar_card_candidate" value="{{ old('aadhar_card_candidate') }}" placeholder="Aadhar Card No. *" required>
                                            <label for="aadhar_card_candidate">Aadhar Card No. *</label>
                                        </div>
                                    </div>

                                    <h5>Details of Id proof : Father / Mother</h5>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="voter_id" name="voter_id" value="{{ old('voter_id') }}" placeholder="Voter Id *" required>
                                            <label for="voter_id">Voter Id *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="number" class="form-control border-2" id="aadhar_card_parent" name="aadhar_card_parent" value="{{ old('aadhar_card_parent') }}" placeholder="Aadhar Card No. *" required>
                                            <label for="aadhar_card_parent">Aadhar Card No. *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control border-2" id="driving_licence" name="driving_licence" value="{{ old('driving_licence') }}" placeholder="Driving Licence">
                                            <label for="driving_licence">Driving Licence</label>
                                        </div>
                                    </div>
                                    <div></div>
                                    
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="file" class="form-control border-2" id="photo" name="photo" value="{{ old('photo') }}" placeholder="Passport Size Photo *" required>
                                            <label for="photo">Passport Size Photo *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="file" class="form-control border-2" id="aadhaar_photo" name="aadhaar_photo" value="{{ old('aadhaar_photo') }}" placeholder="Aadhaar Card *" required>
                                            <label for="aadhaar_photo">Aadhaar Card *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="file" class="form-control border-2" id="transfer" name="transfer" value="{{ old('transfer') }}" placeholder="Transfer Certificate">
                                            <label for="transfer">Transfer Certificate</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="file" class="form-control border-2" id="caste_photo" name="caste_photo" value="{{ old('caste_photo') }}" placeholder="Caste Certificate">
                                            <label for="caste_photo">Caste Certificate</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="file" class="form-control border-2" id="resident" name="resident" value="{{ old('resident') }}" placeholder="Resident Proof">
                                            <label for="resident">Resident Proof</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="file" class="form-control border-2" id="birth_certificate_photo" name="birth_certificate_photo" value="{{ old('birth_certificate_photo') }}" placeholder="Birth Certificate *" required>
                                            <label for="birth_certificate_photo">Birth Certificate *</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-floating">
                                            <input type="file" class="form-control border-2" id="ration_card" name="ration_card" value="{{ old('ration_card') }}" placeholder="Ration Card *" required>
                                            <label for="ration_card">Ration Card *</label>
                                        </div>
                                    </div>
                                    <div class="col-12 text-center">
                                        <button class="btn btn-danger col-md-4 p-2" type="reset">Reset</button>
                                        <button class="btn btn-primary col-md-4 p-2" type="submit">Submit</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Addmission End -->
@endsection

@push('script')
    <script>
        // Get today's date
        let today = new Date();

        // Calculate the date 3 years back
        let threeYearsBack = new Date(today);
        threeYearsBack.setFullYear(today.getFullYear() - 3);

        // Calculate the date 25 years back
        let twentyFiveYearsBack = new Date(today);
        twentyFiveYearsBack.setFullYear(today.getFullYear() - 25);

        // Format the dates as YYYY-MM-DD
        function formatDate(date) {
            let year = date.getFullYear();
            let month = String(date.getMonth() + 1).padStart(2, '0');
            let day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // Set the min and max attributes of the date input
        let dob = document.getElementById('dob');
        dob.min = formatDate(twentyFiveYearsBack);
        dob.max = formatDate(threeYearsBack);
    </script>
@endpush
