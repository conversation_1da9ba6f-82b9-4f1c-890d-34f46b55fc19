@php
    use App\Models\Setting;
    $logo=Setting::where('key_name','logo')->pluck('value')->first();
    $favicon=Setting::where('key_name','favicon')->pluck('value')->first();
    $website_name=Setting::where('key_name','website_name')->pluck('value')->first();
@endphp
<!DOCTYPE html>

<html
lang="en"
class="light-style layout-menu-fixed layout-compact"
dir="ltr">
<head>
    <meta charset="utf-8" />
    <meta
    name="viewport"
    content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title>BSPS School</title>

    <meta name="description" content="" />

    <!-- Favicon -->
    @isset($favicon)
        <link href="{{ asset('assets/'.$favicon) }}" rel="icon">
    @endisset

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap"
    rel="stylesheet" />

    <link rel="stylesheet" href="{{ asset('assets/app/vendor/fonts/materialdesignicons.css') }}" />

    <!-- Menu waves for no-customizer fix -->
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/libs/node-waves/node-waves.css') }}" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/css/core.css') }}" class="template-customizer-core-css" />
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/css/theme-default.css') }}" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="{{ asset('assets/app/css/demo.css') }}" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/libs/perfect-scrollbar/perfect-scrollbar.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/app/vendor/libs/apex-charts/apex-charts.css') }}" />

    <!-- Page CSS -->

    <!-- Helpers -->
    <script src="{{ asset('assets/app/vendor/js/helpers.js') }}"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script src="{{ asset('assets/app/js/config.js') }}"></script>

    <!-- Alertify js -->
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify_theme.css') }}">
    <script src="{{ asset('assets/alertify/alertify.js') }}"></script>

    <link rel="stylesheet" href="{{ asset('assets/datatables/datatables.min.css') }}">
    <style>
        :root{
            --primarycolor:hsl(214, 9%, 35%);
            --secondarycolor:#b18378;
            --thirdcolor:hsl(215, 20%, 33%);
        }
        .color1{
            background: rgb(201,60,179);
            background: linear-gradient(90deg, rgba(201,60,179,1) 0%, rgba(188,20,162,1) 55%, rgba(201,60,179,1) 100%);
        }
        .color2{
            background: rgb(17,111,202);
            background: linear-gradient(90deg, rgba(17,111,202,1) 0%, rgba(60,132,201,1) 55%, rgba(60,132,201,1) 100%);
        }
        .color3{
            background: rgb(252,176,69);
            background: linear-gradient(90deg, rgba(252,176,69,1) 0%, rgba(252,105,50,1) 57%, rgba(252,176,69,1) 100%);
        }
        .add_button{
            margin-right:20px !important;
        }
        @media (max-width:992px){
            .add_button{
                margin-right:0px !important;
            }
            
            button[type='reset'],button[type='submit']{
                width:100%;
                margin-top:5px;
            }
        }
        /* *{
            font-size: calc(1.2625rem + 0.15vw);;
        } */
        .btn-danger{
            color: #8a8d93 !important;
            background-color: #f7f7f7 !important;
            border-color: #c5c6c9 !important;
        }
    </style>
    @stack('style')
</head>

<body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
    <div class="layout-container">
        <!-- Menu -->

        <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
        <div class="app-brand demo">
            @if (isset($logo))
                <img src="{{ asset('assets/'.$logo) }}" alt="Logo Image" loading="lazy" height="50px;">
            @else
                <img src="{{ asset('assets/logo1.png') }}" alt="Logo Image" loading="lazy" height="50px;">
            @endif
        </div>

        <div class="menu-inner-shadow"></div>

        <ul class="menu-inner py-1">
            <li class="menu-item {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                <a href="{{ route('dashboard') }}" 
                    class="menu-link">
                    <i class="menu-icon tf-icons mdi mdi-view-dashboard-outline"></i>
                    <div data-i18n="Email">Dashboard</div>
                </a>
            </li>

            @if (Auth::user()->user_role=="superadmin" || Auth::user()->user_role=="admin")
                <li class="menu-item  {{ request()->routeIs('banner.*') ? 'active open' : '' }}">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <i class="menu-icon tf-icons mdi mdi-image-filter-center-focus-weak"></i>
                        <div data-i18n="Email">Banner(Hero Image)</div>
                    </a>
                    <ul class="menu-sub">
                        <li class="menu-item  {{ request()->routeIs('banner.create') ? 'active' : '' }}">
                            <a href="{{ route('banner.create') }}" class="menu-link">
                                <div data-i18n="Add Banner">Add Banner</div>
                            </a>
                        </li>
                        <li class="menu-item  {{ request()->routeIs('banner.index') ? 'active' : '' }}">
                            <a href="{{ route('banner.index') }}" class="menu-link">
                                <div data-i18n="Banner List">Banner List</div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="menu-item  {{ request()->routeIs('gallery.*') ? 'active open' : '' }}">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <i class="menu-icon tf-icons mdi mdi-view-gallery-outline"></i>
                        <div data-i18n="Email">Gallery</div>
                    </a>
                    <ul class="menu-sub">
                        <li class="menu-item  {{ request()->routeIs('gallery.create') ? 'active' : '' }}">
                            <a href="{{ route('gallery.create') }}" class="menu-link">
                                <div data-i18n="Add Gallery">Add Gallery</div>
                            </a>
                        </li>
                        <li class="menu-item  {{ request()->routeIs('gallery.index') ? 'active' : '' }}">
                            <a href="{{ route('gallery.index') }}" class="menu-link">
                                <div data-i18n="Gallery List">Gallery List</div>
                            </a>
                        </li>
                    </ul>
                </li>
            @endif
            <li class="menu-item {{ request()->routeIs('news.*') ? 'active open' : '' }}">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons mdi mdi-newspaper-variant-outline"></i>
                    <div data-i18n="Email">News</div>
                </a>
                <ul class="menu-sub">
                    <li class="menu-item {{ request()->routeIs('news.create') ? 'active' : '' }}">
                        <a href="{{ route('news.create') }}" class="menu-link">
                            <div data-i18n="Add News">Add News</div>
                        </a>
                    </li>
                    <li class="menu-item {{ request()->routeIs('news.index') ? 'active' : '' }}">
                        <a href="{{ route('news.index') }}" class="menu-link">
                            <div data-i18n="News List">News List</div>
                        </a>
                    </li>
                </ul>
            </li>

            <li class="menu-item  {{ request()->routeIs('event.*') ? 'active open' : '' }}">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons mdi mdi-calendar-text-outline"></i>
                    <div data-i18n="Email">Event</div>
                </a>
                <ul class="menu-sub">
                    <li class="menu-item  {{ request()->routeIs('event.create') ? 'active' : '' }}">
                        <a href="{{ route('event.create') }}" class="menu-link">
                            <div data-i18n="Add Event">Add Event</div>
                        </a>
                    </li>
                    <li class="menu-item  {{ request()->routeIs('event.index') ? 'active' : '' }}">
                        <a href="{{ route('event.index') }}" class="menu-link">
                            <div data-i18n="Event List">Event List</div>
                        </a>
                    </li>
                </ul>
            </li>

            @if (Auth::user()->user_role=="superadmin" || Auth::user()->user_role=="admin")

                <li class="menu-item  {{ request()->routeIs('syllabus.*') ? 'active open' : '' }}">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <i class="menu-icon tf-icons mdi mdi-apps"></i>
                        <div data-i18n="Email">Syllabus</div>
                    </a>
                    <ul class="menu-sub">
                        <li class="menu-item  {{ request()->routeIs('syllabus.create') ? 'active' : '' }}">
                            <a href="{{ route('syllabus.create') }}" class="menu-link">
                                <div data-i18n="Add Syllabus">Add Syllabus</div>
                            </a>
                        </li>
                        <li class="menu-item  {{ request()->routeIs('syllabus.index') ? 'active' : '' }}">
                            <a href="{{ route('syllabus.index') }}" class="menu-link">
                                <div data-i18n="Syllabus List">Syllabus List</div>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="menu-item  {{ request()->routeIs('admission.*') ? 'active' : '' }}">
                    <a href="{{ route('admission.index') }}"
                        class="menu-link">
                        <i class="menu-icon tf-icons mdi mdi-email-open-outline"></i>
                        <div data-i18n="Email">Admission</div>
                    </a>
                </li>

                <li class="menu-item  {{ request()->routeIs('download.*') ? 'active open' : '' }}">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <i class="menu-icon tf-icons mdi mdi-download-circle-outline"></i>
                        <div data-i18n="Email">Download File</div>
                    </a>
                    <ul class="menu-sub">
                        <li class="menu-item  {{ request()->routeIs('download.create') ? 'active' : '' }}">
                            <a href="{{ route('download.create') }}" class="menu-link">
                                <div data-i18n="Add Download File">Add Download File</div>
                            </a>
                        </li>
                        <li class="menu-item  {{ request()->routeIs('download.index') ? 'active' : '' }}">
                            <a href="{{ route('download.index') }}" class="menu-link">
                                <div data-i18n="Download File List">Download File List</div>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="menu-item  {{ request()->routeIs('enquiry.*') ? 'active' : '' }}">
                    <a href="{{ route('enquiry.index') }}"
                        class="menu-link">
                        <i class="menu-icon tf-icons mdi mdi-email-outline"></i>
                        <div data-i18n="Email">Enquiry</div>
                    </a>
                </li>


                <li class="menu-item  {{ request()->routeIs('about') || request()->routeIs('school-detail') || request()->routeIs('social-link') || request()->routeIs('website-detail') ? 'open' : '' }}">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <i class="menu-icon tf-icons mdi mdi-cog-outline"></i>
                        <div data-i18n="setting">Settings</div>
                    </a>
                    
                    <ul class="menu-sub">
                        <li class="menu-item  {{ request()->routeIs('about') ? 'active' : '' }}">
                            <a
                                href="{{ route('about') }}"
                                class="menu-link">
                                <div data-i18n="Email">About Us</div>
                            </a>
                        </li>

                        <li class="menu-item  {{ request()->routeIs('school-detail') ? 'active' : '' }}">
                            <a
                                href="{{ route('school-detail') }}"
                                class="menu-link">
                                <div data-i18n="Email">School Detail</div>
                            </a>
                        </li>

                        <li class="menu-item  {{ request()->routeIs('social-link') ? 'active' : '' }}">
                            <a
                                href="{{ route('social-link') }}"
                                class="menu-link">
                                <div data-i18n="Email">Social Link</div>
                            </a>
                        </li>
    
                        @if (Auth::user() && Auth::user()->user_role=="superadmin")
                            <li class="menu-item  {{ request()->routeIs('website-detail') ? 'active' : '' }}">
                                <a
                                    href="{{ route('website-detail') }}"
                                    class="menu-link">
                                    <div data-i18n="Website Detail">Website Detail</div>
                                </a>
                            </li>
                        @endif
        
                    </ul>
                </li>
            @endif

            @if (Auth::user() && Auth::user()->user_role=="superadmin" || Auth::user()->user_role=="admin")
                <li class="menu-item  {{ request()->routeIs('staff.*') ? 'active open' : '' }}">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <i class="menu-icon tf-icons mdi mdi-account-outline"></i>
                        <div data-i18n="Staff">Staff</div>
                    </a>
                    <ul class="menu-sub">
                        <li class="menu-item  {{ request()->routeIs('staff.create') ? 'active' : '' }}">
                            <a href="{{ route('staff.create') }}" class="menu-link">
                                <div data-i18n="Add Staff">Add Staff</div>
                            </a>
                        </li>
                        <li class="menu-item  {{ request()->routeIs('staff.index') ? 'active' : '' }}">
                            <a href="{{ route('staff.index') }}" class="menu-link">
                                <div data-i18n="Staff List">Staff List</div>
                            </a>
                        </li>
                    </ul>
                </li>
            @endif

            <li class="menu-item  {{ request()->routeIs('admin.recycle-bin') ? 'active' : '' }}">
                <a href="{{ route('admin.recycle-bin') }}"
                    class="menu-link">
                    <i class="menu-icon tf-icons mdi mdi-recycle-variant"></i>
                    <div data-i18n="Email">Recycle Bin</div>
                </a>
            </li>

            {{-- <li class="menu-item">
                <a href="{{ route('logout') }}"
                    class="menu-link">
                    <i class="menu-icon tf-icons mdi mdi-account-outline"></i>
                    <div data-i18n="Email">Logout</div>
                </a>
            </li> --}}
        </ul>
        </aside>
        <!-- / Menu -->

        <!-- Layout container -->
        <div class="layout-page">
        <!-- Navbar -->

        <nav
            class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme"
            id="layout-navbar">
            <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
            <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
                <i class="mdi mdi-menu mdi-24px"></i>
            </a>
            </div>

            <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
            <!-- Search -->
            <div class="navbar-nav align-items-center">
                {{-- <div class="nav-item d-flex align-items-center">
                <i class="mdi mdi-magnify mdi-24px lh-0"></i>
                <input
                    type="text"
                    class="form-control border-0 shadow-none bg-body"
                    placeholder="Search..."
                    aria-label="Search..." />
                </div> --}}
            </div>
            <!-- /Search -->

            <ul class="navbar-nav flex-row align-items-center ms-auto">
                <!-- Place this tag where you want the button to render. -->
                <li class="nav-item lh-1 me-3">
                    
                </li>

                <!-- User -->
                <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a
                    class="nav-link dropdown-toggle hide-arrow p-0"
                    href="javascript:void(0);"
                    data-bs-toggle="dropdown">
                    <div class="avatar avatar-online">
                    <img src="{{ asset('assets/app/img/avatars/1.png') }}" alt class="w-px-40 h-auto rounded-circle" />
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end mt-3 py-2">
                    <li>
                    <a class="dropdown-item pb-2 mb-1" href="#">
                        <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-2 pe-1">
                            <div class="avatar avatar-online">
                            <img src="{{ asset('assets/app/img/avatars/1.png') }}" alt class="w-px-40 h-auto rounded-circle" />
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                            {{-- <small class="text-muted">{{ ucfirst(Auth::user()->user_role) }}</small> --}}
                        </div>
                        </div>
                    </a>
                    </li>
                    <li>
                    <div class="dropdown-divider my-1"></div>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ route('user.index') }}">
                            <i class="mdi mdi-account-outline me-1 mdi-20px"></i>
                            <span class="align-middle">My Profile</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ route('user.password') }}">
                            <i class="mdi mdi-eye-off-outline"></i>
                            <span class="flex-grow-1 align-middle ms-1">Password</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ route('logout') }}">
                            <i class="mdi mdi-power me-1 mdi-20px"></i>
                            <span class="align-middle">Log Out</span>
                        </a>
                    </li>
                </ul>
                </li>
                <!--/ User -->
            </ul>
            </div>
        </nav>

        <!-- / Navbar -->

        <!-- Content wrapper -->
        <div class="content-wrapper">
            <!-- Content -->
            <div class="container">

                @yield('content')

            </div>
            <!-- / Content -->

            <!-- Footer -->
            <footer class="content-footer footer bg-footer-theme">
            <div class="container-xxl">
                <div
                    class="footer-container d-flex align-items-center justify-content-between py-3 flex-md-row flex-column">
                    <div class="text-body mb-2 mb-md-0">
                        © @isset($website_name) {{ $website_name }} @endisset , All Right Reserved. {{ date('Y') }}
                    </div>
                </div>
            </div>
            </footer>
            <!-- / Footer -->

            <div class="content-backdrop fade"></div>
        </div>
        <!-- Content wrapper -->
        </div>
        <!-- / Layout page -->
    </div>

    <!-- Overlay -->
    <div class="layout-overlay layout-menu-toggle"></div>
    </div>
    <!-- / Layout wrapper -->

    <!-- Core JS -->
    <!-- build:js assets/vendor/js/core.js -->
    <script src="{{ asset('assets/app/vendor/libs/jquery/jquery.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/libs/popper/popper.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/js/bootstrap.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/libs/node-waves/node-waves.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/libs/perfect-scrollbar/perfect-scrollbar.js') }}"></script>
    <script src="{{ asset('assets/app/vendor/js/menu.js') }}"></script>

    <!-- endbuild -->

    <!-- Vendors JS -->
    <script src="{{ asset('assets/app/vendor/libs/apex-charts/apexcharts.js') }}"></script>

    <!-- Main JS -->
    <script src="{{ asset('assets/app/js/main.js') }}"></script>

    <!-- Page JS -->
    <script src="{{ asset('assets/app/js/dashboards-analytics.js') }}"></script>

    <!-- Place this tag in your head or just before your close body tag. -->
    <script async defer src="https://buttons.github.io/buttons.js"></script>

    {{-- Text Editor --}}
    <script src="{{ asset('assets/ckeditor5/build/ckeditor.js') }}"></script>
    <script>
        ClassicEditor
            .create( document.querySelector( '.editor' ) )
            .catch( error => {
                console.error( error );
        });
    </script>

    {{-- datatables --}}
    <script src="{{ asset('assets/datatables/datatables.min.js') }}"></script>
    <script>
        let table = new DataTable('#datatable');
    </script>

    <script>
        // Admission count
        function admissionCount(){
            $.ajax({
                type: "GET",
                url: "{{ route('admission-count') }}",
                success: function (response) {
                    $('#all_count').html('');
                    $('#approved_count').html('');
                    $('#declined_count').html('');
                    $('#all_count').html(response['all_count']);
                    $('#approved_count').html(response['approved_count']);
                    $('#declined_count').html(response['declined_count']);
                }
            });
        }

        //Admission Mark Read
        function status(id){
            if(confirm('Are You Sure to Update?')){
                var status=$('#status'+id).val();
                $.ajax({
                    type: "GET",
                    url: "{{ route('admission-update') }}",
                    data: {
                        'id':id,
                        'status':status
                    },
                    success: function (response) {
                        if(response==200){
                            alertify.set('notifier','position', 'top-right');
                            alertify.success('Status Updated Successfully!');
                            admissionCount();
                        }else{
                            alertify.set('notifier','position', 'top-right');
                            alertify.error('Status Not Updated!');
                        }
                    }
                });
            }
        }

        // Enquiry count
        function enquiryCount(){
            $.ajax({
                type: "GET",
                url: "{{ route('enquiry-count') }}",
                success: function (response) {
                    console.log(response);
                    $('#all_count').html('');
                    $('#read_count').html('');
                    $('#unread_count').html('');
                    $('#all_count').html(response['all_count']);
                    $('#read_count').html(response['read_count']);
                    $('#unread_count').html(response['unread_count']);
                }
            });
        }

        //Enquiry Mark
        function mark(id){
            if(confirm('Are You Sure to Mark?')){
                var status=$('#status'+id).val();
                $.ajax({
                    type: "GET",
                    url: "{{ route('enquiry-update') }}",
                    data: {
                        'id':id,
                        'status':status
                    },
                    success: function (response) {
                        if(response==200){
                            enquiryCount();
                            alertify.set('notifier','position', 'top-right');
                            alertify.success('Mark Successfully!');
                        }else{
                            alertify.set('notifier','position', 'top-right');
                            alertify.error('Mark Not Successfully!');
                        }
                    }
                });
            }
        }
    </script>

    @stack('script')
</body>
</html>
