@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Banner /</span> 
        @if (isset($edit))
            Update
        @else
            Add
        @endif
    </h4>
    <x-alert></x-alert>
    @if (isset($edit))
        @foreach ($edit as $row)
            
        @endforeach
        <form action="{{ route('banner.update',$row->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
    @else
        <form action="{{ route('banner.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
    @endif
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">@if (isset($edit))
                    Update Banner
                @else
                    Add Banner
                @endif
            </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Title</label>
                        <input type="text" name="title" value="@isset($edit) {{ $row->title }} @endisset" class="form-control">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Description</label>
                        <input type="text" name="description" value="@isset($edit) {{ $row->description }} @endisset" class="form-control">
                    </div>
                    <div class="mt-3 col-md-6">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="0" @isset($edit) {{ $row->status == "0" ? 'selected' : '' }} @endisset>Show</option>
                            <option value="1" @isset($edit) {{ $row->status == "1" ? 'selected' : '' }} @endisset>Hide</option>
                        </select>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label class="form-label">Sequence No.</label>
                        <input type="number" name="sequence_no" @isset($edit)  value="{{ $row->sequence_no }}" @endisset maxlength="3" class="form-control">
                    </div>
                    <div class="mt-3 col-md-12">
                        <label class="form-label">Image <samp class="text-danger">*</samp>(Recommended image size 1366 X 768 pixels)</label>
                        <input type="file" name="image"  class="form-control mb-3" accept="image/*">
                        @isset($edit)
                            <img src="{{ asset('public/image/'.$row->image) }}" alt="Banner Image" width="100px" height="100px"> 
                        @endisset
                    </div>
                </div>
                <div class="mt-3 mb-3">
                    <button class="btn btn-danger me-2" type="reset">Reset</button>
                    <button class="btn btn-primary" type="submit">Submit</button>
                </div>
            </div>
        </div>
    </form>
@endsection