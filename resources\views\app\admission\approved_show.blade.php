@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Admission /</span> Approved List</h4>
    <ul class="nav nav-pills flex-column flex-md-row gap-2 gap-lg-0">
        {{-- count show using admissionCount() --}}
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light" href="{{ route('admission.index') }}">All <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="all_count">0</span></a>
        </li>
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light active" href="{{ route('admission.approved') }}">Approved <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="approved_count">0</span></a>
        </li>
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light" href="{{ route('admission.declined') }}">Declined <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="declined_count">0</span></a>
        </li>
    </ul> 
    <x-alert></x-alert>

    <div class="mt-3 mb-3">
        <form action="{{ route('admission.approved') }}" method="GET">
            <div class="row text-dark mb-2">
                <div class="col-md-1">
                    <label for="" class="form-label">Name :</label>
                </div>
                <div class="col-md-3">
                    <input type="text" name="name" value="{{ $name }}" class="form-control border-2">
                </div>
                <div class="col-md-1"></div>
                <div class="col-md-1">
                    <label for="" class="form-label">Mobile :</label>
                </div>
                <div class="col-md-3">
                    <input type="text" name="mobile1" value="{{ $mobile1 }}" class="form-control border-2">
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary" type="search">Search</button>
                </div>
            </div>
        </form>
    </div>

    <div class="table-responsive" id="approved_table">
        <table class="table table-striped">
            <thead class="table-dark text-center">
                <tr>
                    <th>Action</th>
                    <th>Status</th>
                    <th>SN.</th>
                    <th>Class Pro.</th>
                    <th>Name</th>
                    <th>Father Name</th>
                    <th>Mother Name</th>
                    <th>DOB</th>
                    <th>Gender</th>
                    <th>Address</th>
                    <th>Mobile 1</th>
                </tr>
            </thead>
            <tbody class="table-border-bottom-0">
                @forelse ($approved_admissions as $row)
                    <tr>
                        <td class="text-center">
                            <div class="dropdown">
                                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                <i class="mdi mdi-dots-vertical"></i>
                                </button>
                                <div class="dropdown-menu">
                                <a class="dropdown-item waves-effect" href="{{ route('admission.show',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Show</a>
                                    <form action="{{ route('admission.destroy',$row->id) }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <button  class="dropdown-item waves-effect" type="submit" onclick="return confirm('Are You Sure to Delete?')"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</button>
                                    </form>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            {{-- status() in  layout.app.blade.php --}}
                            <select id="status{{ $row->id }}" onchange="status({{ $row->id }});" class="form-control">
                                <option value="">Select</option>
                                <option value="1" {{ $row->status=="1" ? 'selected' : '' }}>Approved</option>
                                <option value="2" {{ $row->status=="2" ? 'selected' : '' }}>Declined</option>
                            </select>
                        </td>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $row->class_program }}</td>
                        <td>{{ $row->name }}</td>
                        <td>{{ $row->father_name }}</td>
                        <td>{{ $row->mother_name }}</td>
                        <td>{{ \Carbon\Carbon::parse($row->dob)->format('d-M-Y') }}</td>
                        <td>{{ $row->gender }}</td>
                        <td>{{ $row->address }}</td>
                        <td>{{ $row->mobile1 }}</td>
                    </tr>
                @empty
                    <tr>
                        <td class="text-center" colspan="11">No Approved Admission</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        {{ $approved_admissions->links('pagination::bootstrap-5') }}
    </div>
@endsection

@push('script')
    <script>
        admissionCount();
    </script>
@endpush