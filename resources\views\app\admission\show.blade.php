@extends('layout.app')
@section('content')
    {{-- <a href="{{ route('admission.index') }}" class="menu-item border me-2 col-md-2 active">All</a>
    <a href="{{ route('admission.approved') }}" class="menu-item   border me-2 col-md-2">Approved</a>
    <a href="{{ route('admission.declined') }}" class="menu-item   border me-2 col-md-2">Declined</a> --}}
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Admission /</span> All List</h4>
    {{-- count show using admissionCount() --}}
    <ul class="nav nav-pills flex-column flex-md-row gap-2 gap-lg-0">
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light active" href="{{ route('admission.index') }}">All <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="all_count">0</span></a>
        </li>
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light" href="{{ route('admission.approved') }}">Approved <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="approved_count">0</span></a>
        </li>
        <li class="nav-item">
            <a class="nav-link waves-effect waves-light" href="{{ route('admission.declined') }}">Declined <span class="badge rounded-pill badge-center h-px-20 w-px-40 bg-label-light bg-primary ms-1" id="declined_count">0</span></a>
        </li>
    </ul> 
    <x-alert></x-alert>
    <div class="mt-3 mb-3">
        <form action="{{ route('admission.index') }}" method="GET">
            <div class="row text-dark mb-2">
                <div class="col-md-1">
                    <label for="" class="form-label">Name :</label>
                </div>
                <div class="col-md-3">
                    <input type="text" name="name" value="{{ $name }}" class="form-control border-2">
                </div>
                <div class="col-md-1"></div>
                <div class="col-md-1">
                    <label for="" class="form-label">Mobile :</label>
                </div>
                <div class="col-md-3">
                    <input type="text" name="mobile1" value="{{ $mobile1 }}" class="form-control border-2">
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary" type="search">Search</button>
                </div>
            </div>
        </form>
    </div>

    <div class="card">
        <div class="table-responsive text-nowrap">
            <table class="table">
                <thead class="table-light">
                    <tr>
                        <th>Action</th>
                        <th>Status</th>
                        <th>SN.</th>
                        <th>Class Pro.</th>
                        <th>Name</th>
                        <th>Father Name</th>
                        <th>Mother Name</th>
                        <th>DOB</th>
                        <th>Gender</th>
                        <th>Address</th>
                        <th>Mobile 1</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @forelse ($admissions as $row)
                        <tr>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                    <i class="mdi mdi-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                    <a class="dropdown-item waves-effect" href="{{ route('admission.show',$row->id) }}"><i class="mdi mdi-eye-outline me-1"></i> Show</a>
                                        <form action="{{ route('admission.destroy',$row->id) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button  class="dropdown-item waves-effect" type="submit" onclick="return confirm('Are You Sure to Delete?')"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                {{-- status() in  layout.app.blade.php --}}
                                <select id="status{{ $row->id }}" onchange="status({{ $row->id }});" class="form-control">
                                    <option value="">Select</option>
                                    <option value="1" {{ $row->status=="1" ? 'selected' : '' }}>Approved</option>
                                    <option value="2" {{ $row->status=="2" ? 'selected' : '' }}>Declined</option>
                                </select>
                            </td>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $row->class_program }}</td>
                            <td>{{ $row->name }}</td>
                            <td>{{ $row->father_name }}</td>
                            <td>{{ $row->mother_name }}</td>
                            <td>{{ \Carbon\Carbon::parse($row->dob)->format('d-M-Y') }}</td>
                            <td>{{ $row->gender }}</td>
                            <td>{{ $row->address }}</td>
                            <td>{{ $row->mobile1 }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td class="text-center" colspan="7">No Admission</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            {{ $admissions->links('pagination::bootstrap-5') }}
        </div>
    </div>
@endsection

@push('script')
    <script>
        //Mark Read
        // function status(id){
        //     if(confirm('Are You Sure to Update?')){
        //         var status=$('#status'+id).val();
        //         $.ajax({
        //             type: "GET",
        //             url: "{{ route('admission-update') }}",
        //             data: {
        //                 'id':id,
        //                 'status':status
        //             },
        //             success: function (response) {
        //                 if(response==200){
        //                     alertify.set('notifier','position', 'top-right');
        //                     alertify.success('Status Updated Successfully!');
        //                 }else{
        //                     alertify.set('notifier','position', 'top-right');
        //                     alertify.error('Status Not Updated!');
        //                 }
        //             }
        //         });
        //     }
        // }

        // //All Table Show
        // $('#all').click(function (e) { 
        //     e.preventDefault();
        //     $('#all_table').show();
        //     $('#approved_table').hide();
        //     $('#declined_table').hide();
        //     $('#all_list').show();
        //     $('#approved_list').hide();
        //     $('#declined_list').hide();
        // });

        // //Approved Table Show
        // $('#approved').click(function (e) { 
        //     e.preventDefault();
        //     $('#all_table').hide();
        //     $('#approved_table').show();
        //     $('#declined_table').hide();
        //     $('#all_list').hide();
        //     $('#approved_list').show();
        //     $('#declined_list').hide();
        // });

        // //Unread Table Show
        // $('#declined').click(function (e) { 
        //     e.preventDefault();
        //     $('#all_table').hide();
        //     $('#approved_table').hide();
        //     $('#declined_table').show();
        //     $('#all_list').hide();
        //     $('#approved_list').hide();
        //     $('#declined_list').show();
        // });

        admissionCount();
    </script>
@endpush