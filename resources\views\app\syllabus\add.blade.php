@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Syllabus /</span> 
        @if (isset($syllabus))
            Update
        @else
            Add
        @endif
    </h4>
    <div class="card">
        <div class="card-header">
            @if (isset($syllabus))
                <h4>Update Syllabus</h4>
            @else
                <h4>Add Syllabus</h4>
            @endif
        </div>
        <x-alert></x-alert>

        @if (isset($syllabus))
            <form action="{{ route('syllabus.update',$syllabus->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
        @else
            <form action="{{ route('syllabus.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
        @endif

                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Title <samp class="text-danger">*</samp></label>
                            <input type="text" name="title" value="@isset($syllabus) {{ $syllabus->title }} @endisset" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Slug <samp class="text-danger">*</samp></label>
                            <input type="text" name="slug" value="@isset($syllabus) {{ $syllabus->slug }} @endisset" class="form-control" required>
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Session <samp class="text-danger">*</samp></label>
                            <select class="form-control" name="session" required>
                                <option value="2023-24" @isset($syllabus) {{ $syllabus->session == "2023-24" ? 'selected' : '' }} @endisset>2023-24</option>
                                <option value="2022-23" @isset($syllabus) {{ $syllabus->session == "2022-23" ? 'selected' : '' }} @endisset>2022-23</option>
                            </select>
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="0" @isset($syllabus) {{ $syllabus->status == "0" ? 'selected' : '' }} @endisset>Show</option>
                                <option value="1" @isset($syllabus) {{ $syllabus->status == "1" ? 'selected' : '' }} @endisset>Hide</option>
                            </select>
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">PDF <samp class="text-danger">*</samp></label>
                            <input type="file" name="pdf"  class="form-control mb-3" @empty($syllabus->pdf) required @endempty accept=".pdf">
                            @isset($syllabus->pdf)
                                <embed src="{{ asset('public/pdf/'.$syllabus->pdf) }}" width="100px" height="100px" />
                            @endisset
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Image <samp class="text-danger">*</samp>(Recommended image size 250 X 250 pixels)</label>
                            <input type="file" name="image"  class="form-control mb-3" @empty($syllabus->image) required @endempty  accept="image/*">
                            @isset($syllabus->image)
                                <img src="{{ asset('public/image/'.$syllabus->image) }}" alt="Syllabus Image" width="100px" height="100px" />
                            @endisset
                        </div>
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </form>
    </div>
@endsection