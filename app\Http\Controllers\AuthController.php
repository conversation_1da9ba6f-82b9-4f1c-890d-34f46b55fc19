<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
use Hash;
use App\Mail\OtpVerifyMail;
use Mail;
use App\Models\User;
use App\Models\Otp;
use App\Models\News;
use App\Models\Event;
use App\Models\Syllabus;
use App\Models\Admission;
use App\Models\Download;
use App\Models\Enquiry;

class AuthController extends Controller
{
    public function login(Request $request){
        $request->validate(['email'=>'email|required|max:70',
            'password'=>'string|required|min:8|max:12']);
        if(Auth::attempt(['email'=>$request['email'],'password'=>$request['password'],'status'=>0])){
            return to_route('dashboard')->with('success','Login Successfully!');
        }else{
            return back()->with('error','Your Credentials Are Not Match!');
        }
    }

    public function dashboard(){
        $news=News::count();
        $event=Event::count();
        $syllabus=Syllabus::count();
        $admission=Admission::count();
        $download=Download::count();
        $enquiry=Enquiry::count();
        return view('app.dashboard',compact('news','event','syllabus','admission','download','enquiry'));
    }

    public function forgotPassword(){
        return view('app.forgot_password');
    }

    public function generateOtp(Request $request)
    {
        $request->validate(['email'=>'email|required|exists:users,email']);
        $email=$request['email'];
        $otp=rand(100000,999999);
        $data=['subject'=>'Otp Verify For Forgot Password',
            'otp'=>$otp];
        Mail::to($email)->send(new OtpVerifyMail($data));
        Otp::create(['email'=>$email,'otp'=>$otp]);
        return to_route('verify-otp-form',['email'=>$email]);
    }

    public function verifyOtpForm($email){
        return view('app.verify_otp',compact('email'));
    }

    public function verifyOtp(Request $request)
    {
        $request->validate(['otp'=>'required|max:6|min:6']);
        $otp=$request['otp'];
        $email=$request['email'];
        $otp1=Otp::where('email',$email)->orderBy('id','desc')->pluck('otp')->first();
        if($otp1 == $otp){
            return view('app.new_password',compact('email'));
        }else{
            return back()->with('error','Otp is not match');
        }
    }

    public function newPassword(Request $request)
    {
        $request->validate(['password'=>'required|min:8|max:12|confirmed']);
        $email=$request['email'];
        $password=Hash::make($request['password']);
        try {
            User::where('email',$email)->update(['password'=>$password]);
            return to_route('login')->with('success','Password Change Successfully!');
        } catch (\Throwable $th) {
            return back()->with('error','Password Is Not Change!');
        }
    }

    public function logout(){
        session()->flush();
        Auth::logout();
        return to_route('login')->with('success','Logout Successfully!');
    }
}
