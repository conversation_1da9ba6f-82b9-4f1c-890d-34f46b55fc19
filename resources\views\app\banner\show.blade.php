@extends('layout.app')
@section('content')
    <div class="d-flex justify-content-between">
        <div >
            <h4 class="py-3 mb-4"><span class="text-muted fw-light">Banner /</span> List</h4>
        </div>
        <div class="add_button">
            <a href="{{ route('banner.create') }}" class="btn btn-primary mt-2"><i class="mdi mdi-plus me-1"></i>Add</a>
        </div>
    </div>
    <x-alert></x-alert>
    <div class="card">
        <div class="table-responsive text-nowrap">
            <table class="table">
                <thead class="table-light">
                    <tr>
                        <th>Action</th>
                        <th>SN.</th>
                        <th>Title</th>
                        <th>Description</th>
                        <th>Sequence No.</th>
                        <th>Status</th>
                        <th>Image</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @forelse ($banners as $row)
                        <tr>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                    <i class="mdi mdi-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                    <a class="dropdown-item waves-effect" href="{{ route('banner.edit',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Edit</a>
                                        <form action="{{ route('banner.destroy',$row->id) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button  class="dropdown-item waves-effect" type="submit" onclick="return confirm('Are You Sure to Delete?')"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </td>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $row->title }}</td>
                            <td>{{ $row->description }}</td>
                            <td>{{ $row->sequence_no }}</td>
                            <th>{{ $row->status=="0" ? "Show" : "Hide" }}</th>
                            <td><img src="{{ asset('public/image/'.$row->image) }}" alt="Banner Image" width="100px;" height="100px;" loading="lazy"></td>
                        </tr>
                        @empty
                        <tr>
                            <td class="text-center" colspan="7">No Banner</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            {{ $banners->links('pagination::bootstrap-5') }}
        </div>
    </div>
@endsection