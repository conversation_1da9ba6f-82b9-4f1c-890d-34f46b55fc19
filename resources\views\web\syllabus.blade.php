@extends('layout.web')

@push('style')
    <style>
        .embed-pdf {
            display: block; 
            width: 100%; 
            height: 400px; 
        }
        .pdf{
            display:none;
        }
        @media (max-width: 991px) {
            .embed-pdf,.title{
                display: none;
            }
            .pdf{
                display:block;
            }
        }
    </style>
    <x-codepen_card1></x-codepen_card1>
@endpush

@section('content')
    <!-- Gallary-->

    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>@isset($slug) {{ ucfirst($slug) }} @endisset Syllabus</h1><hr>
            </div>
        </div>

        <div class="album py-5 bg-body-tertiary">
            <div class="container">
                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
                    @foreach ($syllabi as $row)
                        <div class="col">
                            <div class="card shadow-sm gradient-card">
                                <div class="card-body">
                                    
                                        <h5 class="card-title">{{ $row->title }}</h5> 
                                    
                                        <p class="card-text">Brief description of the syllabus or key topics covered in this subject. This helps provide a quick overview of what the syllabus includes.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class=" text-center">
                                                <a class="btn btn-outline-light btn-lg btn-block btnalt text-light mr-2" href="{{ asset('public/pdf/'.$row->pdf) }}" target="_blank"><i class="fa-regular fa-eye"></i> Pdf</a>
                                                <a class="btn btn-outline-light btn-lg btn-block btnalt text-light" href="{{ asset('public/pdf/'.$row->pdf) }}" download><i class="fa-regular fa-circle-down"></i> Pdf</a>
                                            </div>
                                        </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>


    <!-- End Gallary-->
@endsection