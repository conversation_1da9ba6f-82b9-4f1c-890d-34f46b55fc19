<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admissions', function (Blueprint $table) {
            $table->id();
            $table->string('session',7)->nullable();
            $table->string('class_program')->nullable();
            $table->string('name',70);
            $table->string('father_name',70);
            $table->string('mother_name',70);
            $table->date('dob');
            $table->string('gender',6);
            $table->string('blood_group',2)->nullable();
            $table->string('parent_occupation',70)->nullable();
            $table->text('address');
            $table->string('caste',6);
            $table->string('mobile1',10);
            $table->string('mobile2',10)->nullable();
            $table->string('email',100)->nullable();
            $table->string('birth_certificate',15);
            $table->string('aadhar_card_candidate',12);
            $table->string('voter_id',10);
            $table->string('aadhar_card_parent',12);
            $table->string('driving_licence',16)->nullable();
            $table->string('photo',30)->nullable();
            $table->string('aadhaar_photo',30)->nullable();
            $table->string('transfer',30)->nullable();
            $table->string('caste_photo',30)->nullable();
            $table->string('resident',30)->nullable();
            $table->string('birth_certificate_photo',30)->nullable();
            $table->string('ration_card',30)->nullable();
            $table->tinyInteger('status')->default(0)->comment('0:select,1:Approved,2:Declined');
            $table->integer('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admissions');
    }
};
