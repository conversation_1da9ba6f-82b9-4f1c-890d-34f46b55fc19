@extends('layout.app')
@section('content')
    <div class="d-flex justify-content-between">
        <div >
            <h4 class="py-3 mb-4"><span class="text-muted fw-light">Staff /</span> List</h4>
        </div>
        <div class="add_button">
            <a href="{{ route('staff.create') }}" class="btn btn-primary mt-2"><i class="mdi mdi-plus me-1"></i>Add</a>
        </div>
    </div>
    <x-alert></x-alert>
    <div class="card">
        <div class="table-responsive text-nowrap">
    
            <div class="row m-3">
                <div class="col-md-3">
                    <label for="" class="fom-control">Name</label>
                    <input type="text" id="name" class="form-control">
                </div>
                <div class="col-md-3 mt-4">
                    <button class="btn btn-sm btn-primary" onclick="staff();">
                        Search
                    </button>
                </div>
            </div>

            <table class="table">
                <thead class="table-light">
                    <tr>
                        <th>Action</th>
                        <th>SN.</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0" id="staff_data">
                    @include('app.staff.staff_data')
                </tbody>
            </table>
            <div id="pagination_links">
                {{ $staffs->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>
@endsection
@push('script')
    <script>
        function staff(){
            let name = $('#name').val();

            $.ajax({
                type: "GET",
                url: "{{ route('staff.index') }}",
                data: {
                    'name': name,
                },
                success: function (response) {
                    $('#staff_data').html('');
                    $('#staff_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });

        }

    // Handle pagination link clicks

    </script>

    @push('value',"let name = $('#name').val();")
    @push('data',"'name': name,")
    @include('layout.pagination')
@endpush