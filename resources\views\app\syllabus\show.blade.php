@extends('layout.app')
@section('content')
    <div class="d-flex justify-content-between">
        <div >
            <h4 class="py-3 mb-4"><span class="text-muted fw-light">Syllabus /</span> List</h4>
        </div>
        <div class="add_button">
            <a href="{{ route('syllabus.create') }}" class="btn btn-primary mt-2"><i class="mdi mdi-plus me-1"></i>Add</a>
        </div>
    </div>
    <x-alert></x-alert>
    <div class="card">
        <div class="table-responsive text-nowrap">
            <table class="table">
                <thead class="table-light">
                    <tr>
                        <th>Action</th>
                        <th>SN.</th>
                        <th>Title</th>
                        <th>Slug</th>
                        <th>Session</th>
                        <th>Status</th>
                        <th>Pdf</th>
                        <th>Image</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @forelse ($syllabi as $row)
                        <tr>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                    <i class="mdi mdi-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                    <a class="dropdown-item waves-effect" href="{{ route('syllabus.edit',$row->id) }}"><i class="mdi mdi-pencil-outline me-1"></i> Edit</a>
                                        <form action="{{ route('syllabus.destroy',$row->id) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button  class="dropdown-item waves-effect" type="submit" onclick="return confirm('Are You Sure to Delete?')"><i class="mdi mdi-trash-can-outline me-1"></i> Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </td>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $row->title }}</td>
                            <td>{{ $row->slug }}</td>
                            <td>{{ $row->session }}</td>
                            <td>{{ $row->status=="0" ? 'Show' : 'Hide' }}</td>
                            <td><a href="{{ asset('public/pdf/'.$row->pdf) }}">View</a></td>
                            <td><img src="{{ asset('public/image/'.$row->image) }}" alt="Syllabus Image" loading="lazy" height="100px;" width="100px;"></td>
                        </tr>
                    @empty
                        <tr>
                            <td class="text-center" colspan="8">No Syllabus</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            {{ $syllabi->links('pagination::bootstrap-5') }}
        </div>
    </div>
@endsection