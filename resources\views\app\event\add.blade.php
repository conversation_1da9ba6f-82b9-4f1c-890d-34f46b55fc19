@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Event /</span> 
        @if (isset($event))
            Update
        @else
            Add
        @endif
    </h4>
    <div class="card">
        <div class="card-header">
            @if (isset($event))
                <h4>Update Event</h4>
            @else
                <h4>Add Event</h4>
            @endif
        </div>
        <x-alert></x-alert>

        @if (isset($event))
            <form action="{{ route('event.update',$event->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
        @else
            <form action="{{ route('event.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
        @endif

                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Title <samp class="text-danger">*</samp></label>
                            <input type="text" name="title" value="@isset($event) {{ $event->title }} @endisset" class="form-control">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date <samp class="text-danger">*</samp></label>
                            <input type="date" name="date" @isset($event) value="{{ $event->date }}" @endisset class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Time <samp class="text-danger">*</samp></label>
                            <input type="time" name="time" @isset($event) value="{{ $event->time }}"  @endisset class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Location <samp class="text-danger">*</samp></label>
                            <input type="text" name="location" value="@isset($event) {{ $event->location }} @endisset" class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Short Description</label>
                            <input type="text" name="short_desc" value="@isset($event) {{ $event->short_desc }} @endisset" class="form-control">
                        </div>
                        <div class="mt-3 col-md-6">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="0" @isset($event) {{ $event->status == "0" ? 'selected' : '' }} @endisset>Show</option>
                                <option value="1" @isset($event) {{ $event->status == "1" ? 'selected' : '' }} @endisset>Hide</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Long Description <samp class="text-danger">*</samp></label>
                        <textarea  class="form-control" name="long_desc" id="long_desc" cols="30" rows="3">@isset($event) {{ $event->long_desc }} @endisset</textarea>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Image <samp class="text-danger">*</samp>(Recommended image size 300*300 pixels)</label>
                        <input type="file" name="image"  class="form-control mb-3" accept="image/*">
                        @isset($event)
                            <img src="{{ asset('public/image/'.$event->image) }}" alt="Event Image" width="100px" height="100px"> 
                        @endisset
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </form>
    </div>
@endsection