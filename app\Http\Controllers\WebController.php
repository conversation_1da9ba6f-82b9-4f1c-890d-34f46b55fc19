<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Banner;
use App\Models\Gallery;
use App\Models\News;
use App\Models\Event;
use App\Models\About;
use App\Models\Syllabus;
use App\Models\Setting;
use App\Models\Download;

class WebController extends Controller
{
    //status 0 mean Show 1 mean Hide
    public function index(){
        $logo=Setting::where('key_name','logo')->pluck('value')->first();
        $website_name=Setting::where('key_name','website_name')->pluck('value')->first();
        $banners=Banner::select('image','title','description')->where('status','0')->orderBy('sequence_no')->get();
        $galleries=Gallery::select('image')->orderBy('sequence_no')->get();
        $news=News::where('status','0')
            ->select('title','date','time','location','short_desc','long_desc','image')
            ->orderBy('id','desc')->limit(10)->get();
        $events=Event::select('title','date','time','location','short_desc','long_desc','image')
            ->limit(10)->get();
        return view('web.index',compact('banners','galleries','news','events','logo','website_name'));
    }

    public function newsArticle(){
        $news=News::where('status','0')
            ->select('title','date','time','location','short_desc','long_desc','image')
            ->orderBy('id','desc')->paginate(10);
        return view('web.news_article',compact('news'));
    }

    public function events(){
        $events=Event::where('status','0')
            ->select('title','date','time','location','short_desc','long_desc','image')
            ->orderBy('id','desc')->paginate(10);
        return view('web.events',compact('events'));
    }

    public function syllabuses(){
        $syllabi=Syllabus::select('title','slug','image')->where('status','0')
            ->groupBy('slug')->orderBy('id','asc')->get();
        return view('web.syllabi',compact('syllabi'));
    }

    public function syllabus($slug){
        $syllabi=Syllabus::where('status','0')->where('slug',$slug)->get();
        return view('web.syllabus',compact('syllabi','slug'));
    }

    public function admissions(){
        return view('web.admissions');
    }

    public function downloads(){
        $downloads=Download::select('title','image','pdf')->where('status','0')->orderBy('id','desc')->get();
        return view('web.downloads',compact('downloads'));
    }

    public function contactUs(){
        $school_name=Setting::where('key_name','school_name')->pluck('value')->first();
        $address=Setting::where('key_name','address')->pluck('value')->first();
        $mobile=Setting::where('key_name','mobile')->pluck('value')->first();
        $email=Setting::where('key_name','email')->pluck('value')->first();
        return view('web.contact_us',compact('school_name','address','mobile','email'));
    }

    public function aboutUs(){
        $about=Setting::where('key_name','about')->pluck('value')->first();
        return view('web.about_us',compact('about'));
    }
}
