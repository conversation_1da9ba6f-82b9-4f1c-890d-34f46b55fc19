<script>
    $(document).on('click', '#pagination_links a', function(e){
        e.preventDefault();
        let url = $(this).attr('href');
        // let name = $('#name').val(); // Include the search term if needed
        @stack('value')

        $.ajax({
            url: url,
            type: "GET",
            data: { 
                // 'name': name
                @stack('data')
            },
            success: function(response) {
                $('#staff_data').html(response.html);
                $('#pagination_links').html(response.pagination);
            }
        });
    });
</script>