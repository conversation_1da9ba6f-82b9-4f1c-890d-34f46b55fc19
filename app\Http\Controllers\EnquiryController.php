<?php

namespace App\Http\Controllers;

use App\Models\Enquiry;
use Illuminate\Http\Request;
use Mail;
use App\Mail\ConfirmationMail;
use App\Mail\EnquiryMail;

class EnquiryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $enquiries=Enquiry::query();
        if(!empty($request['name'])){
            $name=$request['name'];
            $enquiries=$enquiries->where('name','LIKE','%'.$name.'%');
        }else{
            $name='';
        }
        if(!empty($request['mobile'])){
            $mobile=$request['mobile'];
            $enquiries=$enquiries->where('mobile',$mobile);
        }else{
            $mobile='';
        }
        $enquiries=$enquiries->orderBy('id','desc')->paginate(10);
        return view('app.enquiry.show',compact('enquiries','name','mobile'));
    }

    public function read(Request $request)
    {
        $read_enquiries=Enquiry::where('status',1);
        if(!empty($request['name'])){
            $name=$request['name'];
            $read_enquiries=$read_enquiries->where('name','LIKE','%'.$name.'%');
        }else{
            $name='';
        }
        if(!empty($request['mobile'])){
            $mobile=$request['mobile'];
            $read_enquiries=$read_enquiries->where('mobile',$mobile);
        }else{
            $mobile='';
        }
        $read_enquiries=$read_enquiries->orderBy('id','desc')->paginate(10);
        return view('app.enquiry.read_mark_show',compact('read_enquiries','name','mobile'));
    }

    public function unread(Request $request)
    {
        $unread_enquiries=Enquiry::where('status',0);
        if(!empty($request['name'])){
            $name=$request['name'];
            $unread_enquiries=$unread_enquiries->where('name','LIKE','%'.$name.'%');
        }else{
            $name='';
        }
        if(!empty($request['mobile'])){
            $mobile=$request['mobile'];
            $unread_enquiries=$unread_enquiries->where('mobile',$mobile);
        }else{
            $mobile='';
        }
        $unread_enquiries=$unread_enquiries->orderBy('id','desc')->paginate(10);
        return view('app.enquiry.unread_mark_show',compact('unread_enquiries','name','mobile'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['name'=>'required|string|max:70',
            'mobile'=>'required|max:10|min:10',
            'email'=>'nullable|max:100|email',
            'subject'=>'required|string|max:255',
            'message'=>'required|string']);
        try {
            Enquiry::create($request->all());

            //Confirmation Notification Mail
            if(!empty($request['email'])){
                $data=[
                    'title'=>"Enquiry",
                    'name'=>$request['name'],
                ];
                Mail::to($request['email'])->send(new ConfirmationMail($data));
            }

            //Admin Notification Mail
            $data=['name'=>$request['name'],
            'mobile'=>$request['mobile'],
            'subject'=>$request['subject'],
            'message'=>$request['message'],
            ];
            Mail::to('<EMAIL>')->send(new EnquiryMail($data));

            return back()->with('success','Enquiry Added Successfully!');
        } catch (\Exception $ex) { return $ex;
            return back()->with('error','Enquiry Is Not Added!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Enquiry $enquiry)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Enquiry $enquiry)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function enquiryUpdate(Request $request)
    {
        try {
            Enquiry::where('id',$request['id'])->update(['status'=>$request['status']]);
            return 200;
        } catch (\Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Enquiry $enquiry)
    {
        try {
            $enquiry->delete();
            return back()->with('success','Enquiry Is Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Enquiry Is Not Deleted!');
        }
    }

    public function restore($id){
        try {
            Enquiry::where('id',$id)->restore();
            return back()->with('success','Enquiry Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Enquiry Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        try {
            Enquiry::where('id',$id)->forceDelete();
            return back()->with('success','Enquiry Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Enquiry Is Not Deleted!');
        }
    }

    public function count(){
        $all=Enquiry::count();
        $read=Enquiry::where('status',1)->count();
        $unread=Enquiry::where('status',0)->count();
        return response()->json([
            'all_count'=>$all,
            'read_count'=>$read,
            'unread_count'=>$unread,
        ]);
    }
}
