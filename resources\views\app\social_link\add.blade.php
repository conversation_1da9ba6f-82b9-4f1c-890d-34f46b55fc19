@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4"><span class="text-muted fw-light">Settings /</span> Update Social Link</h4>
    <x-alert></x-alert>
    <div class="card">
        <div class="card-header">
            <h4>Update Social Link</h4>
        </div>
            <form action="{{ route('social-link-update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="container">
                    <div class="">
                        <label class="form-label">Twiiter</label>
                        <input type="url" name="twitter" @isset($twitter) value="{{ $twitter }}" @endisset class="form-control" >
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Facebbok</label>
                        <input type="url" name="facebook" @isset($facebook) value="{{ $facebook }}" @endisset class="form-control" >
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Yotube</label>
                        <input type="url" name="youtube" @isset($youtube) value="{{ $youtube }}" @endisset class="form-control">
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Linkedin</label>
                        <input type="url" name="linkedin" @isset($linkedin) value="{{ $linkedin }}"  @endisset class="form-control">
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-sm btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-sm btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </form>
    </div>
@endsection