<?php

namespace App\Http\Controllers;

use App\Models\Download;
use Illuminate\Http\Request;
use App\Trait\SaveFile;

class DownloadController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $downloads=Download::orderBy('id','desc')->paginate(10);
        return view('app.download.show',compact('downloads'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('app.download.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(['title'=>'required|string|max:70',
            'pdf'=>'nullable|mimes:pdf',
            'image'=>'required|mimes:jpeg,jpg,png,webp,gif',
        ]);
        if(empty($request['pdf'])){
            $pdf='NULL';
        }else{
            $pdf=$this->savePdf($request['pdf']);
        }
        $image=$this->saveImage($request['image']);
        try {
            Download::create(array_merge($request->except('_token'),['image'=>$image,'pdf'=>$pdf]));
            return to_route('download.index')->with('success','Download File Added Successfully!');
        } catch (\Exception $ex) { 
            return back()->with('error','Download File Is Not Added!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Download $download)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Download $download)
    {
        $download=Download::where('id',$download->id)->first();
        return view('app.download.add',compact('download'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Download $download)
    {
        $request->validate(['title'=>'required|string|max:70',
        'pdf'=>'nullable|mimes:pdf',
        'image'=>'nullable|mimes:jpeg,jpg,png,webp,gif',
    ]);
    $old_pdf=Download::where('id',$download->id)->pluck('pdf')->first();
    if(!empty($request['pdf'])){
        $pdf=$this->savePdf($request['pdf']);
        if(file_exists(public_path('pdf/'.$old_pdf))){
            unlink(public_path('pdf/'.$old_pdf));
        }
    }else{
        $pdf=$old_pdf;
    }
    $old_image=Download::where('id',$download->id)->pluck('image')->first();
    if(!empty($request['image'])){
        $image=$this->saveImage($request['image']);
        if(file_exists(public_path('image/'.$old_image))){
            unlink(public_path('image/'.$old_image));
        }
    }else{
        $image=$old_image;
    }
    
    try {
        Download::where('id',$download->id)->update(array_merge($request->except('_token','_method'),['image'=>$image,'pdf'=>$pdf]));
        return to_route('download.index')->with('success','Download File Updated Successfully!');
    } catch (\Exception $ex) {
        return back()->with('error','Download File Is Not Updated!');
    }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Download $download)
    {
        try {
            Download::where('id',$download->id)->delete();
            return back()->with('success','Download File Is Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Download File Is Not Deleted!');
        }
    }

    public function restore($id){
        try {
            Download::where('id',$id)->restore();
            return back()->with('success','Download Restore Successfully!');
        } catch (\Exception $ex) {
            return back()->with('success','Download Is Not Restore!');
        }
    }

    public function permanentlyDestroy($id)
    {
        $old_pdf=Download::withTrashed()->where('id',$id)->pluck('pdf')->first();
        $old_image=Download::withTrashed()->where('id',$id)->pluck('image')->first();
        try {
            Download::where('id',$id)->forceDelete();
            if(file_exists(public_path('pdf/'.$old_pdf))){
                unlink(public_path('pdf/'.$old_pdf));
            }
            if(file_exists(public_path('image/'.$old_image))){
                unlink(public_path('image/'.$old_image));
            }
            return back()->with('success','Download Deleted Successfully!');
        } catch (\Exception $ex) {
            return back()->with('error','Download Is Not Deleted!');
        }
    }
}
