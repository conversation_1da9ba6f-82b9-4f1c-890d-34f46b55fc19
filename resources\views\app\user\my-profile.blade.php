@extends('layout.app')
@section('content')
    <h4 class="py-3 mb-4">
        Update Profile
    </h4>
    <div class="card">
        <div class="card-header">
            <h4>Update Profile</h4>
        </div>
        <x-alert></x-alert>
        <div class="container">
            <form action="{{ route('user.update',$user->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="row container">
                    <div class="col-md-6">
                        <label for="" class="form-label">Name</label>
                        <input type="text" name="name" @isset($user) value="{{ $user->name }}" @endisset class="form-control" maxlength="255" required>
                    </div>
                    <div class="col-md-6">
                        <label for="" class="form-label">Email</label>
                        <input type="email" name="email" @isset($user) value="{{ $user->email }}" maxlength="70" @endisset class="form-control" required>
                    </div>
                    <div class="mt-3 mb-3">
                        <button class="btn btn-danger me-2" type="reset">Reset</button>
                        <button class="btn btn-primary" type="submit">Submit</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection